/**
 * 自定义指令：确保下拉框宽度与选择框一致
 * 使用方法：v-select-width
 */

let idCounter = 0

export default {
  mounted(el) {
    // 为每个元素分配唯一ID
    el._selectWidthId = idCounter++

    // 初始设置宽度
    setSelectWidth(el)

    // 监听窗口大小变化，重新设置宽度
    el._resizeHandler = () => {
      setSelectWidth(el)
    }
    window.addEventListener('resize', el._resizeHandler)

    // 监听点击事件，当点击元素时设置宽度
    el._clickHandler = () => {
      setTimeout(() => {
        setSelectWidth(el)
      }, 0)
    }
    el.addEventListener('click', el._clickHandler)

    // 监听focus事件
    el._focusHandler = () => {
      setTimeout(() => {
        setSelectWidth(el)
      }, 0)
    }
    el.addEventListener('focus', el._focusHandler, true)
  },

  updated(el) {
    // 组件更新时重新设置宽度
    setSelectWidth(el)
  },

  unmounted(el) {
    // 清理事件监听和DOM元素
    window.removeEventListener('resize', el._resizeHandler)
    el.removeEventListener('click', el._clickHandler)
    el.removeEventListener('focus', el._focusHandler, true)

    if (el._mutationObserver) {
      el._mutationObserver.disconnect()
      el._mutationObserver = null
    }

    // 移除样式标签
    const styleEl = document.getElementById(`style-${el._selectWidthId}`)
    if (styleEl) {
      styleEl.parentNode.removeChild(styleEl)
    }
  }
}

// 设置下拉框宽度
function setSelectWidth(el) {
  // 获取select元素的宽度
  const width = el.getBoundingClientRect().width

  // 创建或更新样式标签
  let styleEl = document.getElementById(`style-${el._selectWidthId}`)
  if (!styleEl) {
    styleEl = document.createElement('style')
    styleEl.id = `style-${el._selectWidthId}`
    document.head.appendChild(styleEl)
  }

  // 设置CSS，使用属性选择器针对特定的下拉框
  styleEl.textContent = `
    .el-select-dropdown[style*="--el-select-dropdown-id=${el._selectWidthId}"],
    .el-select__popper[style*="--el-select-dropdown-id=${el._selectWidthId}"] {
      min-width: ${width}px !important;
      width: ${width}px !important;
    }
  `

  // 在元素上设置标识，以便下拉框可以匹配
  el.dataset.selectWidthId = el._selectWidthId

  // 使用MutationObserver监听DOM变化，为新出现的下拉框添加ID
  if (!el._mutationObserver) {
    el._mutationObserver = new MutationObserver((mutations) => {
      // 查找所有下拉框
      const dropdowns = document.querySelectorAll('.el-select-dropdown, .el-select__popper')
      dropdowns.forEach(dropdown => {
        // 检查是否与当前select关联
        if (dropdown.contains(document.activeElement) ||
            dropdown.previousElementSibling === el ||
            el.contains(document.activeElement)) {
          // 设置关联ID
          dropdown.style.setProperty('--el-select-dropdown-id', el._selectWidthId)
        }
      })
    })

    // 开始观察document body的变化
    el._mutationObserver.observe(document.body, {
      childList: true,
      subtree: true
    })
  }
}
