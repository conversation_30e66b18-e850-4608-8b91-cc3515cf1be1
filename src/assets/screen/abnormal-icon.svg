<svg width="62" height="61" viewBox="0 0 62 61" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_493_7509)">
<ellipse cx="31.1391" cy="30.5411" rx="22.2884" ry="21.6425" fill="#FF5D40" fill-opacity="0.341176"/>
</g>
<path d="M16.3726 9.55487C20.7023 6.74573 25.7926 5.24636 30.9998 5.24636V3.62317C25.4619 3.62317 20.0485 5.21774 15.4439 8.20524C10.8394 11.1927 7.2506 15.439 5.13137 20.407C3.01213 25.3751 2.45764 30.8417 3.53802 36.1158C4.6184 41.3898 7.28512 46.2343 11.201 50.0367C15.1168 53.839 20.1059 56.4285 25.5373 57.4776C27.207 57.8001 28.8954 57.9725 30.5818 57.997V56.3736C29.0043 56.3493 27.4252 56.1872 25.8634 55.8856C20.7563 54.8991 16.065 52.4643 12.383 48.8889C8.70092 45.3136 6.19341 40.7583 5.17753 35.7991C4.16165 30.8399 4.68304 25.6996 6.67575 21.0282C8.66847 16.3568 12.043 12.364 16.3726 9.55487Z" fill="url(#paint0_linear_493_7509)"/>
<path d="M33.9251 56.2185V57.8512C36.589 57.5795 39.2133 56.9365 41.7148 55.9304C46.8311 53.8726 51.2041 50.3878 54.2807 45.9166C57.3574 41.4455 58.9995 36.1889 58.9995 30.8116H57.3279C57.3279 35.8679 55.7838 40.8107 52.8908 45.0148C49.9979 49.219 45.886 52.4958 41.0751 54.4308C38.7776 55.3548 36.37 55.9531 33.9251 56.2185Z" fill="url(#paint1_linear_493_7509)"/>
<g filter="url(#filter1_f_493_7509)">
<path d="M31 4.37317C46.0705 4.37329 58.25 16.2308 58.25 30.8116C58.25 45.3924 46.0704 57.25 31 57.2501C15.9295 57.2501 3.75004 45.3925 3.75 30.8116C3.75 16.2308 15.9294 4.37317 31 4.37317Z" stroke="url(#paint2_linear_493_7509)" stroke-width="1.5"/>
</g>
<path d="M57.5673 18C57.5673 18.7684 56.9258 19.3913 56.1345 19.3913C55.3431 19.3913 54.7016 18.7684 54.7016 18C54.7016 17.2316 55.3431 16.6087 56.1345 16.6087C56.9258 16.6087 57.5673 17.2316 57.5673 18Z" fill="url(#paint3_linear_493_7509)" fill-opacity="0.788235"/>
<path d="M59.0001 23.5652C59.0001 24.3336 58.3586 24.9565 57.5673 24.9565C56.776 24.9565 56.1345 24.3336 56.1345 23.5652C56.1345 22.7968 56.776 22.1739 57.5673 22.1739C58.3586 22.1739 59.0001 22.7968 59.0001 23.5652Z" fill="url(#paint4_linear_493_7509)" fill-opacity="0.788235"/>
<path d="M59.0001 29.8261C59.0001 30.5945 58.3586 31.2174 57.5673 31.2174C56.776 31.2174 56.1345 30.5945 56.1345 29.8261C56.1345 29.0577 56.776 28.4348 57.5673 28.4348C58.3586 28.4348 59.0001 29.0577 59.0001 29.8261Z" fill="url(#paint5_linear_493_7509)" fill-opacity="0.788235"/>
<path d="M54.7016 13.1304C54.7016 13.8988 54.0601 14.5217 53.2688 14.5217C52.4775 14.5217 51.836 13.8988 51.836 13.1304C51.836 12.362 52.4775 11.7391 53.2688 11.7391C54.0601 11.7391 54.7016 12.362 54.7016 13.1304Z" fill="url(#paint6_linear_493_7509)" fill-opacity="0.788235"/>
<path d="M50.4032 8.26087C50.4032 9.02927 49.7617 9.65217 48.9703 9.65217C48.179 9.65217 47.5375 9.02927 47.5375 8.26087C47.5375 7.49247 48.179 6.86956 48.9703 6.86956C49.7617 6.86956 50.4032 7.49247 50.4032 8.26087Z" fill="url(#paint7_linear_493_7509)" fill-opacity="0.788235"/>
<path d="M44.6719 5.47826C44.6719 6.24666 44.0304 6.86956 43.239 6.86956C42.4477 6.86956 41.8062 6.24666 41.8062 5.47826C41.8062 4.70986 42.4477 4.08696 43.239 4.08696C44.0304 4.08696 44.6719 4.70986 44.6719 5.47826Z" fill="url(#paint8_linear_493_7509)" fill-opacity="0.788235"/>
<path d="M38.9406 4.08696C38.9406 4.85535 38.2991 5.47826 37.5077 5.47826C36.7164 5.47826 36.0749 4.85535 36.0749 4.08696C36.0749 3.31856 36.7164 2.69565 37.5077 2.69565C38.2991 2.69565 38.9406 3.31856 38.9406 4.08696Z" fill="url(#paint9_linear_493_7509)" fill-opacity="0.788235"/>
<path d="M33.2093 3.3913C33.2093 4.1597 32.5678 4.78261 31.7765 4.78261C30.9851 4.78261 30.3436 4.1597 30.3436 3.3913C30.3436 2.62291 30.9851 2 31.7765 2C32.5678 2 33.2093 2.62291 33.2093 3.3913Z" fill="url(#paint10_linear_493_7509)" fill-opacity="0.788235"/>
<g filter="url(#filter2_f_493_7509)">
<ellipse cx="30.9993" cy="30.8117" rx="17.6571" ry="17.1455" fill="#120300" fill-opacity="0.756863"/>
</g>
<g filter="url(#filter3_d_493_7509)">
<path d="M31.1388 9.64859C43.0551 9.64859 52.6779 19.0232 52.6779 30.5412C52.6778 42.0591 43.055 51.4337 31.1388 51.4337C19.2227 51.4336 9.60075 42.059 9.60071 30.5412C9.60071 19.0233 19.2227 9.64874 31.1388 9.64859Z" stroke="url(#paint11_linear_493_7509)" stroke-width="1.5"/>
<path d="M31.1388 9.64859C43.0551 9.64859 52.6779 19.0232 52.6779 30.5412C52.6778 42.0591 43.055 51.4337 31.1388 51.4337C19.2227 51.4336 9.60075 42.059 9.60071 30.5412C9.60071 19.0233 19.2227 9.64874 31.1388 9.64859Z" stroke="url(#paint12_linear_493_7509)" stroke-width="1.5"/>
</g>
<path d="M31.1388 9.27359C43.2517 9.27359 53.0529 18.8058 53.0529 30.5412C53.0528 42.2765 43.2517 51.8087 31.1388 51.8087C19.0261 51.8086 9.22575 42.2764 9.22571 30.5412C9.22571 18.8059 19.026 9.27374 31.1388 9.27359Z" stroke="url(#paint13_radial_493_7509)" stroke-opacity="0.94" stroke-width="0.75"/>
<path d="M31.1388 9.27359C43.2517 9.27359 53.0529 18.8058 53.0529 30.5412C53.0528 42.2765 43.2517 51.8087 31.1388 51.8087C19.0261 51.8086 9.22575 42.2764 9.22571 30.5412C9.22571 18.8059 19.026 9.27374 31.1388 9.27359Z" stroke="url(#paint14_radial_493_7509)" stroke-opacity="0.78" stroke-width="0.75"/>
<mask id="mask0_493_7509" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="8" y="8" width="46" height="45">
<ellipse cx="30.9998" cy="30.8116" rx="22.567" ry="21.913" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_493_7509)">
<ellipse cx="30.9998" cy="30.8116" rx="22.567" ry="21.913" fill="url(#paint15_linear_493_7509)" fill-opacity="0.0823529"/>
<g filter="url(#filter4_f_493_7509)">
<ellipse cx="5.35052" cy="10.3202" rx="5.35052" ry="10.3202" transform="matrix(0.698856 0.715263 -0.73547 0.677557 19.4335 3.21745)" fill="#FF5A3D" fill-opacity="0.113725"/>
</g>
<g filter="url(#filter5_f_493_7509)">
<ellipse cx="41.4475" cy="43.3914" rx="14.6267" ry="14.2029" fill="#FF5D40" fill-opacity="0.32549"/>
</g>
<g filter="url(#filter6_f_493_7509)">
<ellipse cx="7.0364" cy="4.91853" rx="7.0364" ry="4.91853" transform="matrix(0.824205 -0.566291 0.588923 0.808189 37.6864 46.8968)" fill="#FF5E41" fill-opacity="0.458824"/>
</g>
</g>
<g clip-path="url(#clip0_493_7509)">
<path d="M43.6083 33.9673C43.6083 32.7836 43.3182 31.652 42.7888 30.6088C42.1801 26.2618 37.1359 22.8694 31.0042 22.8694C24.8726 22.8694 19.8283 26.2618 19.2196 30.6088C18.6902 31.652 18.4001 32.7849 18.4001 33.9673C18.4001 36.2631 19.4928 38.3677 21.3073 40.0066C22.9501 43.0854 26.6741 45.2355 31.0042 45.2355C35.3344 45.2355 39.0584 43.0867 40.7012 40.0066C42.517 38.369 43.6083 36.2644 43.6083 33.9673Z" fill="#FF570F"/>
<mask id="mask1_493_7509" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="19" y="22" width="24" height="19">
<path d="M42.8497 31.4933C42.8497 36.2566 37.5466 40.1171 31.004 40.1171C24.4613 40.1171 19.1582 36.2566 19.1582 31.4933C19.1582 26.73 24.4613 22.8694 31.004 22.8694C37.5466 22.8694 42.8497 26.73 42.8497 31.4933Z" fill="#D0450A"/>
</mask>
<g mask="url(#mask1_493_7509)">
<g filter="url(#filter7_f_493_7509)">
<circle cx="19.307" cy="24.1677" r="3.30346" fill="#FF814B" fill-opacity="0.211765"/>
</g>
<g filter="url(#filter8_f_493_7509)">
<circle cx="33.3773" cy="27.4712" r="3.30346" fill="#FF804A" fill-opacity="0.254902"/>
</g>
<g filter="url(#filter9_f_493_7509)">
<ellipse cx="13.4106" cy="22.7047" rx="3.8822" ry="5.23763" fill="white"/>
</g>
<g filter="url(#filter10_f_493_7509)">
<ellipse cx="14.4093" cy="30.0761" rx="3.8822" ry="3.8086" fill="white"/>
</g>
<g filter="url(#filter11_f_493_7509)">
<ellipse cx="21.9489" cy="35.3675" rx="4.23353" ry="3.8086" fill="white"/>
</g>
<g filter="url(#filter12_f_493_7509)">
<ellipse cx="37.7792" cy="27.6317" rx="3.34" ry="2.8067" fill="white" fill-opacity="0.796078"/>
</g>
<g filter="url(#filter13_f_493_7509)">
<ellipse cx="42.593" cy="23.1481" rx="3.90966" ry="3.11938" fill="#FFCBB5" fill-opacity="0.784314"/>
</g>
<g filter="url(#filter14_f_493_7509)">
<ellipse cx="33.3557" cy="33.5577" rx="3.90966" ry="3.11938" fill="#FFCBB5" fill-opacity="0.784314"/>
</g>
</g>
<path d="M31.004 22.9622C34.2576 22.9622 37.1993 23.9224 39.3253 25.47C41.4515 27.0179 42.757 29.1488 42.757 31.4935C42.7569 33.838 41.4514 35.9681 39.3253 37.5159C37.1992 39.0637 34.2578 40.0237 31.004 40.0237C27.7503 40.0237 24.8088 39.0637 22.6827 37.5159C20.5566 35.9681 19.2512 33.838 19.2511 31.4935C19.2511 29.1488 20.5566 27.0179 22.6827 25.47C24.8088 23.9224 27.7504 22.9622 31.004 22.9622Z" stroke="url(#paint16_linear_493_7509)" stroke-width="0.185819"/>
<g filter="url(#filter15_d_493_7509)">
<path d="M31.004 22.9622C34.2576 22.9622 37.1993 23.9224 39.3253 25.47C41.4515 27.0179 42.757 29.1488 42.757 31.4935C42.7569 33.838 41.4514 35.9681 39.3253 37.5159C37.1992 39.0637 34.2578 40.0237 31.004 40.0237C27.7503 40.0237 24.8088 39.0637 22.6827 37.5159C20.5566 35.9681 19.2512 33.838 19.2511 31.4935C19.2511 29.1488 20.5566 27.0179 22.6827 25.47C24.8088 23.9224 27.7504 22.9622 31.004 22.9622Z" stroke="url(#paint17_linear_493_7509)" stroke-width="0.185819"/>
</g>
<mask id="mask2_493_7509" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="18" y="30" width="26" height="14">
<path d="M43.6081 33.9972C43.6081 36.2891 42.5167 38.3911 40.7009 40.0261C38.3895 42.1099 34.9036 43.4353 31.004 43.4353C27.1044 43.4353 23.6184 42.1099 21.307 40.0261C19.4912 38.3911 18.3999 36.2904 18.3999 33.9972C18.3999 32.8161 18.69 31.6845 19.2207 30.64C19.2051 30.7493 19.192 30.8598 19.1829 30.9704C19.1777 31.0367 19.1725 31.1031 19.1686 31.1694C19.1621 31.2878 19.1582 31.4074 19.1582 31.5271C19.1582 32.954 19.6356 34.3003 20.4798 35.4852C22.4504 38.2493 26.4241 40.1393 31.004 40.1393C35.5839 40.1393 39.5563 38.2493 41.5282 35.4852C42.3724 34.3003 42.8497 32.954 42.8497 31.5271C42.8497 31.4074 42.8458 31.2878 42.8393 31.1694C42.8354 31.1018 42.8302 31.0354 42.825 30.9704C42.8159 30.8598 42.8029 30.7493 42.7873 30.64C43.318 31.6832 43.6081 32.8148 43.6081 33.9972Z" fill="#B43C09"/>
<path d="M43.6081 33.9972C43.6081 36.2891 42.5167 38.3911 40.7009 40.0261C38.3895 42.1099 34.9036 43.4353 31.004 43.4353C27.1044 43.4353 23.6184 42.1099 21.307 40.0261C19.4912 38.3911 18.3999 36.2904 18.3999 33.9972C18.3999 32.8161 18.69 31.6845 19.2207 30.64C19.2051 30.7493 19.192 30.8598 19.1829 30.9704C19.1777 31.0367 19.1725 31.1031 19.1686 31.1694C19.1621 31.2878 19.1582 31.4074 19.1582 31.5271C19.1582 32.954 19.6356 34.3003 20.4798 35.4852C22.4504 38.2493 26.4241 40.1393 31.004 40.1393C35.5839 40.1393 39.5563 38.2493 41.5282 35.4852C42.3724 34.3003 42.8497 32.954 42.8497 31.5271C42.8497 31.4074 42.8458 31.2878 42.8393 31.1694C42.8354 31.1018 42.8302 31.0354 42.825 30.9704C42.8159 30.8598 42.8029 30.7493 42.7873 30.64C43.318 31.6832 43.6081 32.8148 43.6081 33.9972Z" fill="url(#paint18_linear_493_7509)" fill-opacity="0.2"/>
</mask>
<g mask="url(#mask2_493_7509)">
<g filter="url(#filter16_i_493_7509)">
<path d="M43.6081 33.9972C43.6081 36.2891 42.5167 38.3911 40.7009 40.0261C38.3895 42.1099 34.9036 43.4353 31.004 43.4353C27.1044 43.4353 23.6184 42.1099 21.307 40.0261C19.4912 38.3911 18.3999 36.2904 18.3999 33.9972C18.3999 32.8161 18.69 31.6845 19.2207 30.64C19.2051 30.7493 19.192 30.8598 19.1829 30.9704C19.1777 31.0367 19.1725 31.1031 19.1686 31.1694C19.1621 31.2878 19.1582 31.4074 19.1582 31.5271C19.1582 32.954 19.6356 34.3003 20.4798 35.4852C22.4504 38.2493 26.4241 40.1393 31.004 40.1393C35.5839 40.1393 39.5563 38.2493 41.5282 35.4852C42.3724 34.3003 42.8497 32.954 42.8497 31.5271C42.8497 31.4074 42.8458 31.2878 42.8393 31.1694C42.8354 31.1018 42.8302 31.0354 42.825 30.9704C42.8159 30.8598 42.8029 30.7493 42.7873 30.64C43.318 31.6832 43.6081 32.8148 43.6081 33.9972Z" fill="#B43C09"/>
<path d="M43.6081 33.9972C43.6081 36.2891 42.5167 38.3911 40.7009 40.0261C38.3895 42.1099 34.9036 43.4353 31.004 43.4353C27.1044 43.4353 23.6184 42.1099 21.307 40.0261C19.4912 38.3911 18.3999 36.2904 18.3999 33.9972C18.3999 32.8161 18.69 31.6845 19.2207 30.64C19.2051 30.7493 19.192 30.8598 19.1829 30.9704C19.1777 31.0367 19.1725 31.1031 19.1686 31.1694C19.1621 31.2878 19.1582 31.4074 19.1582 31.5271C19.1582 32.954 19.6356 34.3003 20.4798 35.4852C22.4504 38.2493 26.4241 40.1393 31.004 40.1393C35.5839 40.1393 39.5563 38.2493 41.5282 35.4852C42.3724 34.3003 42.8497 32.954 42.8497 31.5271C42.8497 31.4074 42.8458 31.2878 42.8393 31.1694C42.8354 31.1018 42.8302 31.0354 42.825 30.9704C42.8159 30.8598 42.8029 30.7493 42.7873 30.64C43.318 31.6832 43.6081 32.8148 43.6081 33.9972Z" fill="url(#paint19_linear_493_7509)" fill-opacity="0.2" style="mix-blend-mode:overlay"/>
</g>
<g filter="url(#filter17_f_493_7509)">
<circle cx="16.3695" cy="27.3733" r="2.64136" fill="#FFC9B2" fill-opacity="0.364706"/>
</g>
<g filter="url(#filter18_f_493_7509)">
<ellipse cx="37.1255" cy="30.0147" rx="2.64136" ry="5.1515" transform="rotate(-48.6638 37.1255 30.0147)" fill="#FFC8B1" fill-opacity="0.168627"/>
</g>
<g filter="url(#filter19_f_493_7509)">
<circle cx="16.849" cy="34.2948" r="2.62551" fill="#FFC8B1" fill-opacity="0.141176"/>
</g>
</g>
<path d="M42.9321 31.1752C43.3101 32.0662 43.5151 33.014 43.5151 33.9974C43.5151 36.2579 42.4393 38.3364 40.6392 39.9574C38.347 42.0238 34.8838 43.3421 31.0044 43.3422C27.125 43.3422 23.6619 42.0237 21.3696 39.9574C19.5694 38.3364 18.4928 36.2592 18.4927 33.9974C18.4927 33.0157 18.6972 32.0685 19.0747 31.1771C19.0686 31.2926 19.0649 31.4095 19.0649 31.5267C19.0649 32.9752 19.5498 34.3406 20.4038 35.5394C22.3958 38.3335 26.4021 40.2318 31.0044 40.2318C35.6065 40.2317 39.6107 38.3334 41.604 35.5394C42.458 34.3406 42.9429 32.9752 42.9429 31.5267C42.9429 31.4089 42.9383 31.2913 42.9321 31.1752Z" stroke="url(#paint20_radial_493_7509)" stroke-opacity="0.831373" stroke-width="0.185819"/>
<path d="M42.9321 31.1752C43.3101 32.0662 43.5151 33.014 43.5151 33.9974C43.5151 36.2579 42.4393 38.3364 40.6392 39.9574C38.347 42.0238 34.8838 43.3421 31.0044 43.3422C27.125 43.3422 23.6619 42.0237 21.3696 39.9574C19.5694 38.3364 18.4928 36.2592 18.4927 33.9974C18.4927 33.0157 18.6972 32.0685 19.0747 31.1771C19.0686 31.2926 19.0649 31.4095 19.0649 31.5267C19.0649 32.9752 19.5498 34.3406 20.4038 35.5394C22.3958 38.3335 26.4021 40.2318 31.0044 40.2318C35.6065 40.2317 39.6107 38.3334 41.604 35.5394C42.458 34.3406 42.9429 32.9752 42.9429 31.5267C42.9429 31.4089 42.9383 31.2913 42.9321 31.1752Z" stroke="url(#paint21_radial_493_7509)" stroke-opacity="0.831373" stroke-width="0.185819"/>
<path d="M42.9321 31.1752C43.3101 32.0662 43.5151 33.014 43.5151 33.9974C43.5151 36.2579 42.4393 38.3364 40.6392 39.9574C38.347 42.0238 34.8838 43.3421 31.0044 43.3422C27.125 43.3422 23.6619 42.0237 21.3696 39.9574C19.5694 38.3364 18.4928 36.2592 18.4927 33.9974C18.4927 33.0157 18.6972 32.0685 19.0747 31.1771C19.0686 31.2926 19.0649 31.4095 19.0649 31.5267C19.0649 32.9752 19.5498 34.3406 20.4038 35.5394C22.3958 38.3335 26.4021 40.2318 31.0044 40.2318C35.6065 40.2317 39.6107 38.3334 41.604 35.5394C42.458 34.3406 42.9429 32.9752 42.9429 31.5267C42.9429 31.4089 42.9383 31.2913 42.9321 31.1752Z" stroke="url(#paint22_radial_493_7509)" stroke-opacity="0.831373" stroke-width="0.185819"/>
<path d="M40.7335 39.9858C38.4221 42.08 34.9349 43.4119 31.0353 43.4119C27.1357 43.4119 23.6484 42.08 21.337 39.9858C22.9799 43.0763 26.7039 45.2342 31.0353 45.2342C35.3667 45.2342 39.0907 43.0776 40.7335 39.9858Z" fill="url(#paint23_radial_493_7509)" fill-opacity="0.92549"/>
<path d="M31.099 21.3528C26.5126 21.3528 22.76 25.1015 22.76 29.6827V34.0623C22.76 38.6434 26.5126 42.3921 31.099 42.3921C35.6854 42.3921 39.438 38.6434 39.438 34.0623V29.6827C39.438 25.1015 35.6854 21.3528 31.099 21.3528Z" fill="url(#paint24_linear_493_7509)"/>
<g opacity="0.53" filter="url(#filter20_if_493_7509)">
<path d="M31.099 15.0859C26.5126 15.0859 22.76 18.8346 22.76 23.4158V27.7953C22.76 32.3765 26.5126 36.1252 31.099 36.1252C35.6854 36.1252 39.438 32.3765 39.438 27.7953V23.4158C39.438 18.8346 35.6854 15.0859 31.099 15.0859Z" fill="#D9D9D9" fill-opacity="0.01"/>
</g>
<g filter="url(#filter21_d_493_7509)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M31.1992 17.8616C34.1479 17.8616 36.5374 20.2511 36.5374 23.1998V27.6249C36.5374 30.5737 34.1479 32.9631 31.1992 32.9631C28.2504 32.9631 25.861 30.5737 25.861 27.6249V23.1998C25.861 20.2511 28.2504 17.8616 31.1992 17.8616Z" fill="url(#paint25_linear_493_7509)" shape-rendering="crispEdges"/>
</g>
<mask id="mask3_493_7509" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="22" y="14" width="18" height="22">
<path d="M31.0989 14.4355C26.5125 14.4355 22.7599 18.1842 22.7599 22.7654V27.145C22.7599 31.7261 26.5125 35.4748 31.0989 35.4748C35.6852 35.4748 39.4379 31.7261 39.4379 27.145V22.7654C39.4379 18.1842 35.6852 14.4355 31.0989 14.4355Z" fill="#C14A17" fill-opacity="0.88"/>
</mask>
<g mask="url(#mask3_493_7509)">
<g filter="url(#filter22_di_493_7509)">
<path d="M31.0989 14.4355C26.5125 14.4355 22.7599 18.1842 22.7599 22.7654V27.145C22.7599 31.7261 26.5125 35.4748 31.0989 35.4748C35.6852 35.4748 39.4379 31.7261 39.4379 27.145V22.7654C39.4379 18.1842 35.6852 14.4355 31.0989 14.4355Z" fill="#FE6A2A"/>
</g>
<g filter="url(#filter23_f_493_7509)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M31.1471 -2.47398C38.331 -2.47398 44.1544 4.10641 44.1544 12.2243C44.1544 20.3421 38.331 26.9225 31.1471 26.9225C23.9631 26.9225 18.1398 20.3421 18.1398 12.2243C18.1398 4.10641 23.9631 -2.47398 31.1471 -2.47398Z" fill="#C0440F" fill-opacity="0.913725"/>
</g>
</g>
<g filter="url(#filter24_f_493_7509)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M30.9338 27.3817C34.1141 27.3817 37.0316 26.2566 39.3105 24.3822C39.3105 24.429 39.3144 24.4746 39.3144 24.5214C39.3144 29.0388 35.6528 32.7017 31.1341 32.7017C26.6153 32.7017 23.0539 29.1351 22.959 24.7009C25.1741 26.384 27.9369 27.383 30.9338 27.383V27.3817Z" fill="url(#paint26_linear_493_7509)" fill-opacity="0.0745098"/>
</g>
<mask id="mask4_493_7509" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="22" y="14" width="18" height="22">
<path d="M30.9753 14.4355C26.389 14.4355 22.6364 18.1842 22.6364 22.7654V27.145C22.6364 31.7261 26.389 35.4748 30.9753 35.4748C35.5617 35.4748 39.3143 31.7261 39.3143 27.145V22.7654C39.3143 18.1842 35.5617 14.4355 30.9753 14.4355Z" fill="#C14A17"/>
</mask>
<g mask="url(#mask4_493_7509)">
<path d="M30.9753 14.4355C26.389 14.4355 22.6364 18.1842 22.6364 22.7654V27.145C22.6364 31.7261 26.389 35.4748 30.9753 35.4748C35.5617 35.4748 39.3143 31.7261 39.3143 27.145V22.7654C39.3143 18.1842 35.5617 14.4355 30.9753 14.4355Z" fill="#FF793F" fill-opacity="0.0156863"/>
<g filter="url(#filter25_f_493_7509)">
<ellipse cx="20.6995" cy="22.1514" rx="3.7766" ry="3.78462" fill="#FFAC89" fill-opacity="0.52549"/>
</g>
<g filter="url(#filter26_f_493_7509)">
<ellipse cx="26.1824" cy="32.0952" rx="3.7766" ry="3.78462" fill="#FFAC89" fill-opacity="0.52549"/>
</g>
<g filter="url(#filter27_f_493_7509)">
<ellipse cx="21.6179" cy="14.0355" rx="3.7766" ry="1.88973" transform="rotate(-47.4526 21.6179 14.0355)" fill="#FF844F" fill-opacity="0.952941"/>
</g>
<g filter="url(#filter28_f_493_7509)">
<ellipse cx="32.8627" cy="23.0392" rx="2.14907" ry="3.96922" fill="#FF753A" fill-opacity="0.498039"/>
</g>
</g>
<mask id="mask5_493_7509" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="22" y="14" width="18" height="22">
<path d="M30.9757 14.4355C26.3893 14.4355 22.6367 18.1842 22.6367 22.7654V27.145C22.6367 31.7261 26.3893 35.4748 30.9757 35.4748C35.5621 35.4748 39.3147 31.7261 39.3147 27.145V22.7654C39.3147 18.1842 35.5621 14.4355 30.9757 14.4355Z" fill="#C14A17"/>
</mask>
<g mask="url(#mask5_493_7509)">
<path d="M30.9757 14.4355C26.3893 14.4355 22.6367 18.1842 22.6367 22.7654V27.145C22.6367 31.7261 26.3893 35.4748 30.9757 35.4748C35.5621 35.4748 39.3147 31.7261 39.3147 27.145V22.7654C39.3147 18.1842 35.5621 14.4355 30.9757 14.4355Z" fill="#FF793F" fill-opacity="0.0156863"/>
<g filter="url(#filter29_f_493_7509)">
<ellipse cx="19.6758" cy="15.2187" rx="3.7766" ry="3.78462" fill="#FFC4AA" fill-opacity="0.470588"/>
</g>
<g filter="url(#filter30_f_493_7509)">
<ellipse cx="26.3652" cy="10.759" rx="3.7766" ry="3.78462" fill="#FFC8B0" fill-opacity="0.701961"/>
</g>
<g filter="url(#filter31_f_493_7509)">
<ellipse cx="33.6122" cy="14.0109" rx="3.7766" ry="3.78462" fill="#FFC8B0" fill-opacity="0.701961"/>
</g>
<g filter="url(#filter32_f_493_7509)">
<ellipse cx="32.4826" cy="25.9052" rx="2.14907" ry="3.15844" fill="#FFC8B1" fill-opacity="0.709804"/>
</g>
</g>
<g style="mix-blend-mode:overlay">
<mask id="mask6_493_7509" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="22" y="14" width="18" height="22">
<path d="M31.0538 14.4355C26.4675 14.4355 22.7148 18.1842 22.7148 22.7654V27.145C22.7148 31.7261 26.4675 35.4748 31.0538 35.4748C35.6402 35.4748 39.3928 31.7261 39.3928 27.145V22.7654C39.3928 18.1842 35.6402 14.4355 31.0538 14.4355Z" fill="#C14A17"/>
</mask>
<g mask="url(#mask6_493_7509)">
<path d="M31.0538 14.4355C26.4675 14.4355 22.7148 18.1842 22.7148 22.7654V27.145C22.7148 31.7261 26.4675 35.4748 31.0538 35.4748C35.6402 35.4748 39.3928 31.7261 39.3928 27.145V22.7654C39.3928 18.1842 35.6402 14.4355 31.0538 14.4355Z" fill="#FF793F" fill-opacity="0.0156863"/>
<g filter="url(#filter33_f_493_7509)">
<ellipse cx="17.7246" cy="21.7811" rx="3.7766" ry="7.08185" fill="#FFC4AA" fill-opacity="0.470588"/>
</g>
<g filter="url(#filter34_f_493_7509)">
<ellipse cx="33.4712" cy="21.282" rx="2.14907" ry="3.15844" fill="#FFC8B1" fill-opacity="0.709804"/>
</g>
</g>
<g filter="url(#filter35_f_493_7509)">
<ellipse cx="30.9655" cy="26.2201" rx="4.65004" ry="4.55458" fill="white" fill-opacity="0.57"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_i_493_7509" x="8.85071" y="8.89859" width="44.5768" height="43.285" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.875"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.373333 0 0 0 0 0.262745 0 0 0 0.443137 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_493_7509"/>
</filter>
<filter id="filter1_f_493_7509" x="1" y="1.62317" width="59.9995" height="58.3768" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter2_f_493_7509" x="0.342163" y="0.666229" width="61.3142" height="60.2909" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="6.5" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter3_d_493_7509" x="6.60071" y="6.64859" width="49.0768" height="47.785" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.125"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.36 0 0 0 0 0.247059 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_493_7509"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_493_7509" result="shape"/>
</filter>
<filter id="filter4_f_493_7509" x="-0.380737" y="-1.43579" width="31.9264" height="30.9456" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3.75" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter5_f_493_7509" x="17.0708" y="19.4385" width="48.7535" height="47.9058" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.875" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter6_f_493_7509" x="31.6484" y="33.0088" width="29.468" height="27.7568" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.125" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter7_f_493_7509" x="13.2163" y="18.0769" width="12.1815" height="12.1815" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.39364" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter8_f_493_7509" x="27.2866" y="21.3804" width="12.1815" height="12.1815" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.39364" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter9_f_493_7509" x="6.0908" y="14.0295" width="14.6397" height="17.3505" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.71882" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter10_f_493_7509" x="7.08945" y="22.8299" width="14.6397" height="14.4925" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.71882" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter11_f_493_7509" x="14.2777" y="28.1213" width="15.3423" height="14.4925" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.71882" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter12_f_493_7509" x="31.0016" y="21.3873" width="13.5552" height="12.4887" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.71882" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter13_f_493_7509" x="35.2457" y="16.5911" width="14.6946" height="13.114" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.71882" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter14_f_493_7509" x="26.0084" y="27.0007" width="14.6946" height="13.114" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.71882" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter15_d_493_7509" x="18.6009" y="22.312" width="24.8064" height="18.3626" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.278728"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.906667 0 0 0 0 0.866667 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_493_7509"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_493_7509" result="shape"/>
</filter>
<filter id="filter16_i_493_7509" x="18.3999" y="30.64" width="25.2081" height="12.7953" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.557456"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.357647 0 0 0 0 0.0823529 0 0 0 0.19 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_493_7509"/>
</filter>
<filter id="filter17_f_493_7509" x="11.4054" y="22.4092" width="9.92818" height="9.92818" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.16137" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter18_f_493_7509" x="30.5585" y="23.753" width="13.134" height="12.5233" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.16137" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter19_f_493_7509" x="11.9008" y="29.3465" width="9.89644" height="9.89647" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.16137" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter20_if_493_7509" x="22.3884" y="13.8781" width="17.4213" height="22.6188" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2.50855"/>
<feGaussianBlur stdDeviation="0.603911"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0901961 0 0 0 0 0.032549 0 0 0 0 0.00784314 0 0 0 0.44 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_493_7509"/>
<feGaussianBlur stdDeviation="0.185819" result="effect2_foregroundBlur_493_7509"/>
</filter>
<filter id="filter21_d_493_7509" x="23.1666" y="15.1673" width="16.0651" height="20.4902" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.34719"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.442745 0 0 0 0 0.203922 0 0 0 0.73 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_493_7509"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_493_7509" result="shape"/>
</filter>
<filter id="filter22_di_493_7509" x="19.7868" y="11.4624" width="22.6242" height="26.9855" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.48655"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.346667 0 0 0 0 0.0666667 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_493_7509"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_493_7509" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.975548"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.489412 0 0 0 0 0.270588 0 0 0 0.54 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_493_7509"/>
</filter>
<filter id="filter23_f_493_7509" x="17.3965" y="-3.21726" width="27.5012" height="30.8831" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.371637" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter24_f_493_7509" x="22.7732" y="24.1964" width="16.727" height="8.69111" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.0929093" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter25_f_493_7509" x="13.2994" y="14.7433" width="14.8002" height="14.8162" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.81173" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter26_f_493_7509" x="18.7823" y="24.6871" width="14.8002" height="14.8162" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.81173" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter27_f_493_7509" x="16.2003" y="8.46453" width="10.8353" height="11.142" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.25428" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter28_f_493_7509" x="27.0902" y="15.4465" width="11.545" height="15.1854" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.81173" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter29_f_493_7509" x="12.5544" y="8.08933" width="14.2427" height="14.2587" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.67237" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter30_f_493_7509" x="18.9652" y="3.35095" width="14.8002" height="14.8162" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.81173" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter31_f_493_7509" x="26.2121" y="6.60278" width="14.8002" height="14.8162" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.81173" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter32_f_493_7509" x="27.2675" y="19.6807" width="10.4301" height="12.4489" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.533" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter33_f_493_7509" x="10.6033" y="11.3546" width="14.2427" height="20.8532" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.67237" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter34_f_493_7509" x="28.2561" y="15.0575" width="10.4301" height="12.4489" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.533" result="effect1_foregroundBlur_493_7509"/>
</filter>
<filter id="filter35_f_493_7509" x="22.9707" y="18.3207" width="15.9895" height="15.7986" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.67237" result="effect1_foregroundBlur_493_7509"/>
</filter>
<linearGradient id="paint0_linear_493_7509" x1="30.9998" y1="3.62317" x2="30.9998" y2="57.997" gradientUnits="userSpaceOnUse">
<stop stop-color="#922714" stop-opacity="0.572549"/>
<stop offset="1" stop-color="#EF7661" stop-opacity="0.572549"/>
</linearGradient>
<linearGradient id="paint1_linear_493_7509" x1="30.9998" y1="3.62317" x2="30.9998" y2="57.997" gradientUnits="userSpaceOnUse">
<stop stop-color="#922714" stop-opacity="0.572549"/>
<stop offset="1" stop-color="#EF7661" stop-opacity="0.572549"/>
</linearGradient>
<linearGradient id="paint2_linear_493_7509" x1="47.716" y1="34.8695" x2="31.6518" y2="58.4443" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF6E54" stop-opacity="0"/>
<stop offset="0.440004" stop-color="#FF6E54" stop-opacity="0.556863"/>
<stop offset="1" stop-color="#FF6E54" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_493_7509" x1="33.839" y1="2" x2="59.0471" y2="31.9512" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF411F" stop-opacity="0.0313726"/>
<stop offset="0.256832" stop-color="#F14D30" stop-opacity="0.666667"/>
<stop offset="0.51925" stop-color="#F15337" stop-opacity="0.917647"/>
<stop offset="0.805492" stop-color="#F46B53" stop-opacity="0.776471"/>
<stop offset="1" stop-color="#FF8B77" stop-opacity="0.0588235"/>
</linearGradient>
<linearGradient id="paint4_linear_493_7509" x1="33.839" y1="2" x2="59.0471" y2="31.9512" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF411F" stop-opacity="0.0313726"/>
<stop offset="0.256832" stop-color="#F14D30" stop-opacity="0.666667"/>
<stop offset="0.51925" stop-color="#F15337" stop-opacity="0.917647"/>
<stop offset="0.805492" stop-color="#F46B53" stop-opacity="0.776471"/>
<stop offset="1" stop-color="#FF8B77" stop-opacity="0.0588235"/>
</linearGradient>
<linearGradient id="paint5_linear_493_7509" x1="33.839" y1="2" x2="59.0471" y2="31.9512" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF411F" stop-opacity="0.0313726"/>
<stop offset="0.256832" stop-color="#F14D30" stop-opacity="0.666667"/>
<stop offset="0.51925" stop-color="#F15337" stop-opacity="0.917647"/>
<stop offset="0.805492" stop-color="#F46B53" stop-opacity="0.776471"/>
<stop offset="1" stop-color="#FF8B77" stop-opacity="0.0588235"/>
</linearGradient>
<linearGradient id="paint6_linear_493_7509" x1="33.839" y1="2" x2="59.0471" y2="31.9512" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF411F" stop-opacity="0.0313726"/>
<stop offset="0.256832" stop-color="#F14D30" stop-opacity="0.666667"/>
<stop offset="0.51925" stop-color="#F15337" stop-opacity="0.917647"/>
<stop offset="0.805492" stop-color="#F46B53" stop-opacity="0.776471"/>
<stop offset="1" stop-color="#FF8B77" stop-opacity="0.0588235"/>
</linearGradient>
<linearGradient id="paint7_linear_493_7509" x1="33.839" y1="2" x2="59.0471" y2="31.9512" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF411F" stop-opacity="0.0313726"/>
<stop offset="0.256832" stop-color="#F14D30" stop-opacity="0.666667"/>
<stop offset="0.51925" stop-color="#F15337" stop-opacity="0.917647"/>
<stop offset="0.805492" stop-color="#F46B53" stop-opacity="0.776471"/>
<stop offset="1" stop-color="#FF8B77" stop-opacity="0.0588235"/>
</linearGradient>
<linearGradient id="paint8_linear_493_7509" x1="33.839" y1="2" x2="59.0471" y2="31.9512" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF411F" stop-opacity="0.0313726"/>
<stop offset="0.256832" stop-color="#F14D30" stop-opacity="0.666667"/>
<stop offset="0.51925" stop-color="#F15337" stop-opacity="0.917647"/>
<stop offset="0.805492" stop-color="#F46B53" stop-opacity="0.776471"/>
<stop offset="1" stop-color="#FF8B77" stop-opacity="0.0588235"/>
</linearGradient>
<linearGradient id="paint9_linear_493_7509" x1="33.839" y1="2" x2="59.0471" y2="31.9512" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF411F" stop-opacity="0.0313726"/>
<stop offset="0.256832" stop-color="#F14D30" stop-opacity="0.666667"/>
<stop offset="0.51925" stop-color="#F15337" stop-opacity="0.917647"/>
<stop offset="0.805492" stop-color="#F46B53" stop-opacity="0.776471"/>
<stop offset="1" stop-color="#FF8B77" stop-opacity="0.0588235"/>
</linearGradient>
<linearGradient id="paint10_linear_493_7509" x1="33.839" y1="2" x2="59.0471" y2="31.9512" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF411F" stop-opacity="0.0313726"/>
<stop offset="0.256832" stop-color="#F14D30" stop-opacity="0.666667"/>
<stop offset="0.51925" stop-color="#F15337" stop-opacity="0.917647"/>
<stop offset="0.805492" stop-color="#F46B53" stop-opacity="0.776471"/>
<stop offset="1" stop-color="#FF8B77" stop-opacity="0.0588235"/>
</linearGradient>
<linearGradient id="paint11_linear_493_7509" x1="17.8518" y1="13.4768" x2="43.4402" y2="48.3295" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF583A" stop-opacity="0.843137"/>
<stop offset="0.378498" stop-color="#FF5C3F" stop-opacity="0.172549"/>
<stop offset="1" stop-color="#FF5739" stop-opacity="0.847059"/>
</linearGradient>
<linearGradient id="paint12_linear_493_7509" x1="17.8518" y1="13.4768" x2="43.4402" y2="48.3295" gradientUnits="userSpaceOnUse">
<stop offset="0.927285" stop-color="#FFA797" stop-opacity="0"/>
<stop offset="0.964559" stop-color="#FFA797" stop-opacity="0.823529"/>
</linearGradient>
<radialGradient id="paint13_radial_493_7509" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(42.7195 47.8761) rotate(156.787) scale(10.9288 10.712)">
<stop stop-color="#FFD8D1"/>
<stop offset="1" stop-color="#FFD8D1" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint14_radial_493_7509" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(17.7528 16.3877) rotate(75.9367) scale(10.6289 10.9092)">
<stop stop-color="#FFD8D1"/>
<stop offset="1" stop-color="#FFD8D1" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint15_linear_493_7509" x1="10.9403" y1="38.5218" x2="48.816" y2="16.0509" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF927F" stop-opacity="0.01"/>
<stop offset="0.306213" stop-color="#FF927F" stop-opacity="0.01"/>
<stop offset="0.636305" stop-color="#FF927F" stop-opacity="0.01"/>
<stop offset="1" stop-color="#FF5C3F" stop-opacity="0.0470588"/>
</linearGradient>
<linearGradient id="paint16_linear_493_7509" x1="31.0041" y1="22.8694" x2="31.0041" y2="40.1171" gradientUnits="userSpaceOnUse">
<stop offset="0.220968" stop-color="#FF773D" stop-opacity="0"/>
<stop offset="1" stop-color="#FF773D" stop-opacity="0.258824"/>
</linearGradient>
<linearGradient id="paint17_linear_493_7509" x1="26.1199" y1="23.8244" x2="39.7799" y2="36.8635" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE4D9"/>
<stop offset="0.361946" stop-color="#FFE4D9" stop-opacity="0"/>
<stop offset="0.673297" stop-color="#FFE4D9" stop-opacity="0"/>
<stop offset="1" stop-color="#FFE4D9"/>
</linearGradient>
<linearGradient id="paint18_linear_493_7509" x1="18.3999" y1="43.4353" x2="43.6081" y2="43.4353" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.385387"/>
<stop offset="0.658846"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint19_linear_493_7509" x1="18.3999" y1="43.4353" x2="43.6081" y2="43.4353" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.385387"/>
<stop offset="0.658846"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<radialGradient id="paint20_radial_493_7509" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(41.7537 38.5273) rotate(152.241) scale(11.2661 2.35493)">
<stop offset="0.250271" stop-color="#FF5D18"/>
<stop offset="1" stop-color="#FF5D18" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint21_radial_493_7509" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(39.1957 37.0377) rotate(154.493) scale(7.26721 1.51906)">
<stop offset="0.250271" stop-color="#FF5D17" stop-opacity="0.215686"/>
<stop offset="1" stop-color="#FF5D18" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint22_radial_493_7509" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(25.9636 42.4826) rotate(-140.972) scale(5.51352 2.77266)">
<stop offset="0.250271" stop-color="#FF5D17" stop-opacity="0.215686"/>
<stop offset="1" stop-color="#FF5D18" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint23_radial_493_7509" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(31.0353 42.098) rotate(89.9999) scale(3.13624 9.69824)">
<stop offset="0.608161" stop-color="#B23601"/>
<stop offset="0.811056" stop-color="#B23601" stop-opacity="0.79"/>
<stop offset="1" stop-color="#B13500" stop-opacity="0.62"/>
</radialGradient>
<linearGradient id="paint24_linear_493_7509" x1="31.099" y1="21.3528" x2="31.099" y2="42.3921" gradientUnits="userSpaceOnUse">
<stop offset="0.704272" stop-color="#FF824D" stop-opacity="0.4"/>
<stop offset="0.886105" stop-color="#FF824D" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint25_linear_493_7509" x1="31.1992" y1="17.8616" x2="31.1992" y2="32.9631" gradientUnits="userSpaceOnUse">
<stop offset="0.48165" stop-color="#FFCEB9"/>
<stop offset="0.719274" stop-color="#FF9A6F" stop-opacity="0.627451"/>
<stop offset="1" stop-color="#FF9A6F" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint26_linear_493_7509" x1="22.959" y1="29.6947" x2="39.3144" y2="29.6947" gradientUnits="userSpaceOnUse">
<stop offset="0.173737" stop-color="#FF976B" stop-opacity="0.49"/>
<stop offset="0.51349" stop-color="#FF9466" stop-opacity="0.4"/>
<stop offset="0.853243" stop-color="#FF976B"/>
</linearGradient>
<clipPath id="clip0_493_7509">
<rect width="33.6" height="33.6" fill="white" transform="translate(14.2 13.0355)"/>
</clipPath>
</defs>
</svg>
