/* Element Plus 样式覆盖 */

/* 设置表单中所有下拉框的固定宽度 */
.el-form-item .el-select,
.el-form-item .el-tree-select,
.el-form-item .el-input {
  width: 373px !important;
}

/* 确保下拉框与输入框宽度一致 */
.el-select-dropdown,
.el-select__popper.el-popper[role="tooltip"],
.el-popper.is-pure.el-select__popper {
  min-width: 373px !important;
  width: 373px !important;
}

/* 设置内部容器的宽度 */
.el-select-dropdown__wrap,
.el-select-dropdown__list {
  width: 373px;
}

/* 强制覆盖内联样式 */
.el-select-dropdown[style*="min-width"] {
  min-width: 373px !important;
  width: 373px !important;
}

/* 特殊情况处理 */
.search-container .search-select,
.search-container .search-input {
  width: 280px !important;
}

/* 强制搜索容器中的下拉框宽度为280px */
.search-container .el-select-dropdown,
.search-container .el-select__popper.el-popper[role="tooltip"],
.search-container .el-popper.is-pure.el-select__popper,
body > .el-popper.is-pure.el-select__popper[style*="position: absolute"],
body > .el-select-dropdown[style*="position: absolute"] {
  min-width: 280px !important;
  width: 280px !important;
  max-width: 280px !important;
}

/* 覆盖内联样式 */
.el-select-dropdown[style*="min-width"],
.el-select-dropdown[style*="width"],
.el-popper[style*="min-width"],
.el-popper[style*="width"] {
  min-width: var(--select-width, 280px) !important;
  width: var(--select-width, 280px) !important;
}

/* 全局确保下拉框宽度与触发器一致 */
.el-select__popper {
  width: var(--el-select-input-width, 100%) !important;
  min-width: var(--el-select-input-width, 100%) !important;
}

/* 设置CSS变量以传递宽度信息 */
.el-select .el-input {
  --el-select-input-width: 100%;
}