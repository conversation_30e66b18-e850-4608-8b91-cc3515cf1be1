<template>
  <div class="app-container">
    <page-title title="筛选条件">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <div class="form-content">
          <div class="form-items">
            <el-form-item label="房屋名称">
              <el-input v-model="queryParams.houseName" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="启用状态">
              <el-select v-model="queryParams.status" placeholder="请选择" style="width: 200px;">
                <el-option label="启用" :value="0" />
                <el-option label="禁用" :value="1" />
              </el-select>
            </el-form-item>
          </div>
          <div class="form-buttons">
            <el-button @click="resetQuery">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button type="primary" style="background-color: #165DFF; border: none" @click="handleQuery">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
          </div>
        </div>
      </el-form>
    </page-title>

    <page-title title="房屋列表">
      <div class="table-operations">
        <el-button type="primary" style="background-color: #165DFF; border: none" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增房屋
        </el-button>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="房屋编号" prop="houseSno" align="center" />
        <el-table-column label="属地单位" prop="localUnitText" align="center" />
        <el-table-column label="房屋名称" prop="houseName" align="center" />
        <el-table-column label="房屋地址" prop="houseAddress" align="center" />
        <el-table-column label="危房等级" prop="dangerLevelText" align="center" />
        <el-table-column label="责任人" prop="responsiblePerson" align="center" />
        <el-table-column label="责任人电话" prop="responsiblePhone" align="center" />
        <el-table-column label="录入时间" prop="createTime" align="center" />
        <el-table-column label="启用状态" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              active-value="0"
              inactive-value="1"
              @change="() => handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150">
          <template #default="scope">
            <el-button type="text" style="color: #165DFF;" @click="handleView(scope.row)">查看</el-button>
            <el-button type="text" style="color: #165DFF;" @click="handleEdit(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 30, 40]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </page-title>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="960px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      style="margin-top: 25vh;"
    >
      <page-title title="基础信息">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
        >
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="房屋编号" prop="houseSno">
                <el-input v-model="form.houseSno" placeholder="请输入房屋编号" :disabled="dialogType === 'edit'" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="归属单位" prop="localUnit">
                <el-tree-select
                  v-model="form.localUnit"
                  :data="unitOptions"
                  :props="{ label: 'label', value: 'id', children: 'children' }"
                  value-key="id"
                  placeholder="请选择归属单位"
                  check-strictly
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="房屋名称" prop="houseName">
                <el-input v-model="form.houseName" placeholder="请输入房屋名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="房屋地址" prop="houseAddress">
                <el-input v-model="form.houseAddress" placeholder="请输入房屋地址" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="责任人" prop="responsiblePerson">
                <el-input v-model="form.responsiblePerson" placeholder="请输入责任人" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="责任人电话" prop="responsiblePhone">
                <el-input v-model="form.responsiblePhone" placeholder="请输入责任人电话" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="危房等级" prop="dangerLevel">
                <el-select v-model="form.dangerLevel" placeholder="请选择" style="width: 100%">
                  <el-option
                    v-for="dict in danger_level_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="启用状态" prop="status">
                <el-switch v-model="form.status" active-value="0" inactive-value="1" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </page-title>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" style="background-color: #165DFF; border: none" @click="handleSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      title="房屋详情"
      width="960px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      style="margin-top: 25vh;"
    >
      <page-title title="基础信息">
        <el-descriptions :column="2" label-width="80px">
          <el-descriptions-item label="房屋编号" class="desc-item">{{ detail.houseSno }}</el-descriptions-item>
          <el-descriptions-item label="所属单位" class="desc-item">{{ detail.localUnitText }}</el-descriptions-item>
          <el-descriptions-item label="房屋名称" class="desc-item">{{ detail.houseName }}</el-descriptions-item>
          <el-descriptions-item label="房屋地址" class="desc-item">{{ detail.houseAddress }}</el-descriptions-item>
          <el-descriptions-item label="责任人" class="desc-item">{{ detail.responsiblePerson }}</el-descriptions-item>
          <el-descriptions-item label="责任人电话" class="desc-item">{{ detail.responsiblePhone }}</el-descriptions-item>
          <el-descriptions-item label="接入时间" class="desc-item">{{ detail.createTime }}</el-descriptions-item>
          <el-descriptions-item label="启用状态" class="desc-item">
            <el-tag :type="detail.status === '0' ? 'success' : 'warning'">
              {{ detail.status === '0' ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </page-title>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDetailClose">关闭</el-button>
          <el-button type="primary" style="background-color: #165DFF; border: none" @click="handleDetailEdit">编辑</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="House">
import { ref, reactive, onMounted, nextTick, computed } from 'vue'
import PageTitle from '@/components/PageTitle/index.vue'
import { Search, Refresh, Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { listHouse, switchHouse, addHouse, updateHouse, getHouseDetail } from '@/api/house'
import { deptTreeSelect } from '@/api/system/user'
import { getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance()
const { danger_level_type } = proxy.useDict('danger_level_type')

const queryParams = reactive({
  houseName: '',
  status: '',
  pageNum: 1,
  pageSize: 10
})

const loading = ref(false)
const total = ref(0)
const tableData = ref([])
const initialLoad = ref(true)

// 弹窗控制
const dialogVisible = ref(false)
const dialogType = ref('add') // 'add' or 'edit'
const dialogTitle = ref('')
const dialogRow = ref(null)

// 详情弹窗控制
const detailVisible = ref(false)
const detailId = ref('')

// 新增/编辑表单
const formRef = ref(null)
const form = reactive({
  houseSno: '',
  localUnit: '',
  houseName: '',
  houseAddress: '',
  responsiblePerson: '',
  responsiblePhone: '',
  dangerLevel: '',
  status: '1'
})
const rules = {
  houseSno: [
    { required: true, message: '请输入房屋编号', trigger: 'blur' }
  ],
  localUnit: [
    { required: true, message: '请选择归属单位', trigger: 'blur' }
  ],
  houseName: [
    { required: true, message: '请输入房屋名称', trigger: 'blur' }
  ],
  houseAddress: [
    { required: true, message: '请输入房屋地址', trigger: 'blur' }
  ],
  responsiblePerson: [
    { required: true, message: '请输入责任人', trigger: 'blur' }
  ],
  responsiblePhone: [
    { required: true, message: '请输入责任人电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  dangerLevel: [
    { required: true, message: '请选择危房等级', trigger: 'blur' }
  ]
}
const unitOptions = ref([])

// 详情数据
const detail = reactive({
  houseId: '',
  belongUnit: '',
  houseName: '',
  houseAddress: '',
  owner: '',
  ownerPhone: '',
  joinTime: '',
  status: true
})

// 获取列表数据
const getList = async() => {
  loading.value = true
  try {
    const res = await listHouse(queryParams)
    if (res.code === 200) {
      initialLoad.value = true
      tableData.value = res.rows
      total.value = res.total
      nextTick(() => {
        initialLoad.value = false
      })
    } else {
      ElMessage.error(res.msg || '获取数据失败')
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 查询操作
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置操作
const resetQuery = () => {
  queryParams.houseName = ''
  queryParams.status = ''
  handleQuery()
}

// 新增操作
const handleAdd = () => {
  dialogTitle.value = '新增房屋'
  dialogType.value = 'add'
  Object.assign(form, {
    houseSno: '',
    localUnit: '',
    houseName: '',
    houseAddress: '',
    responsiblePerson: '',
    responsiblePhone: '',
    dangerLevel: '',
    status: '1'
  })
  dialogVisible.value = true
}

// 编辑操作
const handleEdit = (row) => {
  dialogTitle.value = '编辑房屋'
  dialogType.value = 'edit'
  Object.assign(form, row)
  dialogVisible.value = true
}

// 查看操作
const handleView = async(row) => {
  detailId.value = row.houseId
  detailVisible.value = true
  // 获取详情
  try {
    const res = await getHouseDetail(row.houseId)
    if (res.code === 200) {
      Object.assign(detail, res.data)
    } else {
      ElMessage.error(res.message || '获取详情失败')
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取详情失败')
  }
}

// 详情弹窗编辑
const handleDetailEdit = () => {
  dialogTitle.value = '编辑房屋'
  dialogType.value = 'edit'
  Object.assign(form, detail)
  dialogVisible.value = true
  detailVisible.value = false
}

// 关闭弹窗
const handleDialogClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}

const handleDetailClose = () => {
  detailVisible.value = false
}

// 提交表单
const handleSubmit = async() => {
  if (!formRef.value) return
  await formRef.value.validate()
  try {
    const api = dialogType.value === 'add' ? addHouse : updateHouse
    const res = await api(form)
    if (res.code === 200) {
      ElMessage.success(dialogType.value === 'add' ? '新增成功' : '修改成功')
      dialogVisible.value = false
      getList()
    } else {
      ElMessage.error(res.message || (dialogType.value === 'add' ? '新增失败' : '修改失败'))
    }
  } catch (error) {
    console.error(error)
    ElMessage.error(dialogType.value === 'add' ? '新增失败' : '修改失败')
  }
}

// 状态变更
const handleStatusChange = async(row) => {
  // 初始加载时不触发消息提示
  if (initialLoad.value) return

  try {
    const res = await switchHouse(row.houseId)
    if (res.code === 200) {
      ElMessage.success('状态更新成功')
    } else {
      ElMessage.error(res.msg || '状态更新失败')
      // 恢复状态
      row.status = row.status === 1 ? 0 : 1
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('状态更新失败')
    // 恢复状态
    row.status = row.status === 1 ? 0 : 1
  }
}

// 分页操作
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  queryParams.pageNum = 1 // 切换每页显示数量时，重置到第一页
  getList()
}

const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}

onMounted(async() => {
  try {
    const res = await deptTreeSelect()
    if (res.code === 200) {
      unitOptions.value = res.data
    }
  } catch (e) {
    console.error(e)
  }
  getList()
})
</script>

<style scoped>
.form-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.form-items {
  display: flex;
  gap: 20px;
}

.form-buttons {
  display: flex;
  gap: 12px;
}

:deep(.el-button .el-icon) {
  margin-right: 4px;
  vertical-align: middle;
}

:deep(.el-descriptions__label) {
  width: 100px;
  color: #86909C;
  text-align: right;
}

.table-operations {
  margin-bottom: 16px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
