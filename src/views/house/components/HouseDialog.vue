<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="960px"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    style="margin-top: 25vh;"
  >
    <page-title title="基础信息">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="房屋编号" prop="houseSno">
              <el-input v-model="form.houseSno" placeholder="请输入房屋编号" :disabled="type === 'edit'" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属单位" prop="localUnit">
              <el-tree-select
                v-model="form.localUnit"
                :data="unitOptions"
                :props="{ label: 'label', value: 'id', children: 'children' }"
                value-key="id"
                placeholder="请选择归属单位"
                check-strictly
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="房屋名称" prop="houseName">
              <el-input v-model="form.houseName" placeholder="请输入房屋名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="房屋地址" prop="houseAddress">
              <el-input v-model="form.houseAddress" placeholder="请输入房屋地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="责任人" prop="responsiblePerson">
              <el-input v-model="form.responsiblePerson" placeholder="请输入责任人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="责任人电话" prop="responsiblePhone">
              <el-input v-model="form.responsiblePhone" placeholder="请输入责任人电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="危房等级" prop="dangerLevel">
              <el-select v-model="form.dangerLevel" placeholder="请选择" style="width: 100%">
                <el-option
                  v-for="dict in danger_level_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="启用状态" prop="status">
              <el-switch v-model="form.status" active-value="0" inactive-value="1" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </page-title>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" style="background-color: #165DFF; border: none" @click="handleSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, onMounted, computed } from 'vue'
import PageTitle from '@/components/PageTitle/index.vue'
import { addHouse, updateHouse } from '@/api/house'
import { deptTreeSelect } from '@/api/system/user'
import { ElMessage } from 'element-plus'
const { proxy } = getCurrentInstance()
const { danger_level_type } = proxy.useDict('danger_level_type')
const props = defineProps({
  visible: Boolean,
  title: String,
  type: String,
  row: Object
})
const visible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const emit = defineEmits(['update:visible', 'success'])

const formRef = ref(null)

const form = reactive({
  houseName: '',
  houseAddress: '',
  status: true
})

const rules = {
  houseSno: [
    { required: true, message: '请输入房屋编号', trigger: 'blur' }
  ],
  localUnit: [
    { required: true, message: '请输入隶属单位', trigger: 'blur' }
  ],
  houseName: [
    { required: true, message: '请输入房屋名称', trigger: 'blur' }
  ],
  houseAddress: [
    { required: true, message: '请输入房屋地址', trigger: 'blur' }
  ],
  responsiblePerson: [
    { required: true, message: '请输入责任人', trigger: 'blur' }
  ],
  responsiblePhone: [
    { required: true, message: '请输入责任人电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  dangerLevel: [
    { required: true, message: '请选择危房等级', trigger: 'blur' }
  ]
}

const unitOptions = ref([])

onMounted(async() => {
  try {
    const res = await deptTreeSelect()
    if (res.code === 200) {
      unitOptions.value = res.data
    }
  } catch (e) {
    console.error(e)
  }
})

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  console.log('点击了关闭弹窗', visible.value)
  formRef.value?.resetFields()
}

// 提交表单
const handleSubmit = async() => {
  if (!formRef.value) return

  await formRef.value.validate()
  try {
    const api = props.type === 'add' ? addHouse : updateHouse
    const res = await api(form)
    if (res.code === 200) {
      ElMessage.success(props.type === 'add' ? '新增成功' : '修改成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res.message || (props.type === 'add' ? '新增失败' : '修改失败'))
    }
  } catch (error) {
    console.error(error)
    ElMessage.error(props.type === 'add' ? '新增失败' : '修改失败')
  }
}

// 监听visible变化
watch(
  () => props.visible,
  (val) => {
    if (val && props.type === 'edit' && props.row) {
      // 编辑时，填充表单数据
      Object.assign(form, props.row)
    }
    if (!val) {
      formRef.value?.resetFields()
    }
  }
)
</script>

<style scoped>
.dialog-footer {
  padding: 20px 0;
  text-align: right;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  color: #86909C;
}
</style>
