<template>
  <el-dialog
    v-model="visible"
    title="房屋详情"
    width="960px"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    style="margin-top: 25vh;"
  >
    <page-title title="基础信息">
      <el-descriptions :column="2">
        <el-descriptions-item label="房屋编号" class="desc-item">{{ detail.houseId }}</el-descriptions-item>
        <el-descriptions-item label="隶属单位" class="desc-item">{{ detail.belongUnit }}</el-descriptions-item>
        <el-descriptions-item label="房屋名称" class="desc-item">{{ detail.houseName }}</el-descriptions-item>
        <el-descriptions-item label="房屋地址" class="desc-item">{{ detail.houseAddress }}</el-descriptions-item>
        <el-descriptions-item label="责任人" class="desc-item">{{ detail.owner }}</el-descriptions-item>
        <el-descriptions-item label="责任人电话" class="desc-item">{{ detail.ownerPhone }}</el-descriptions-item>
        <el-descriptions-item label="接入时间" class="desc-item">{{ detail.joinTime }}</el-descriptions-item>
        <el-descriptions-item label="启用状态" class="desc-item">
          <el-tag :type="detail.status==='0' ? 'success' : 'warning'">
            {{ detail.status==='0' ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </page-title>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          type="primary"
          style="background-color: #165DFF; border: none"
          @click="handleEdit"
        >编辑</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, watch, computed } from 'vue'
import PageTitle from '@/components/PageTitle/index.vue'
import { getHouseDetail } from '@/api/house'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  houseId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'edit'])

const visible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const detail = reactive({
  houseId: '',
  belongUnit: '',
  houseName: '',
  houseAddress: '',
  owner: '',
  ownerPhone: '',
  joinTime: '',
  status: true
})

// 获取房屋详情
const getDetail = async() => {
  try {
    const res = await getHouseDetail(props.houseId)
    if (res.code === 200) {
      Object.assign(detail, res.data)
    } else {
      ElMessage.error(res.message || '获取详情失败')
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取详情失败')
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 编辑房屋
const handleEdit = () => {
  visible.value = false
  // 通知父组件打开编辑弹窗
  emit('edit', detail)
}

// 监听visible变化
watch(
  () => props.visible,
  (val) => {
    if (val && props.houseId) {
      getDetail()
    }
  }
)
</script>

<style scoped>
:deep(.el-descriptions) {
  padding: 0;
}

:deep(.el-descriptions__header) {
  display: none;
}

:deep(.desc-item) {
  padding: 12px 0;
  margin-right: 32px;
}

:deep(.desc-item .el-descriptions__label) {
  width: 100px;
  color: #86909C;
}

:deep(.desc-item .el-descriptions__content) {
  color: #1D2129;
}

.dialog-footer {
  padding: 20px 0;
  text-align: right;
}
</style>
