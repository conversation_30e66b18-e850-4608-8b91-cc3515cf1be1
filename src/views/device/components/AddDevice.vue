<template>
  <el-dialog
    v-model="dialogVisible"
    style="margin-top: 25vh;"
    append-to-body
    :title="props.editData ? '编辑设备' : '新增设备'"
    width="960px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
    class="add-device-dialog"
  >
    <!-- 第一步：基础信息 -->
    <div v-show="currentStep === 1">
      <page-title title="基础信息">
        <el-form
          ref="baseFormRef"
          :model="baseForm"
          :rules="baseRules"
          label-width="100px"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备名称" prop="deviceName">
                <el-input v-model="baseForm.deviceName" placeholder="请输入" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设备类型" prop="deviceType">
                <el-select v-model="baseForm.deviceType" placeholder="请选择" style="width: 100%">
                  <el-option label="网关" value="wg" />
                  <!-- <el-option label="倾角仪" value="2" /> -->
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备型号" prop="deviceModel">
                <el-input v-model="baseForm.deviceModel" placeholder="请输入" />
                <!-- <el-select v-model="baseForm.deviceModel" placeholder="请选择" style="width: 100%">
                  <el-option label="型号1" value="1" />
                  <el-option label="型号2" value="2" />
                </el-select> -->
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设备序列号" prop="imei">
                <el-input v-model="baseForm.imei" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="安装地址" prop="houseId">
                <el-tree-select
                  v-model="baseForm.houseId"
                  :data="houseTreeData"
                  node-key="value"
                  placeholder="请选择安装地址"
                  filterable
                  check-strictly
                  :props="{
                    children: 'children',
                    label: 'label',
                    value: 'value',
                    disabled: (data) => data.value && String(data.value).startsWith('org_')
                  }"
                  :default-expand-all="true"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="启用状态" prop="status">
                <el-switch
                  v-model="baseForm.status"
                  active-value="0"
                  inactive-value="1"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </page-title>
    </div>

    <!-- 第二步：阈值设置 -->
    <div v-show="currentStep === 2" class="threshold-container">
      <span style="color:#BD5F67">温馨提示：一级>二级>三级</span>
      <page-title
        v-for="(items, typeText) in groupedThresholds"
        :key="typeText"
        :title="typeText"
      >
        <el-table
          :data="items"
          border
          style="width: 100%;border-radius: 4px;"
          class="threshold-table"
        >
          <el-table-column
            label="告警等级"
            prop="alarmLevelText"
            align="center"
          />
          <el-table-column
            label="指标"
            prop="dimensionTypeText"
            align="center"
          />
          <el-table-column
            label="阈值"
            width="300"
            align="center"
          >
            <template #default="scope">
              <el-input v-model="scope.row.thresholdNum" placeholder="请输入阈值" />
            </template>
          </el-table-column>
          <el-table-column
            label="说明"
            prop="remark"
            width="300"
            align="center"
          >
            <template #default="scope">
              <el-input v-model="scope.row.remark" :placeholder="scope.row.remark" disabled />
            </template>
          </el-table-column>
        </el-table>
      </page-title>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <!-- <el-button @click="handleClose">取消</el-button> -->
        <el-button v-if="currentStep === 2" @click="handlePrev">上一步</el-button>
        <el-button
          type="primary"
          :style="{ backgroundColor: '#165DFF', border: 'none' }"
          @click="handleNext"
        >
          {{ currentStep === 1 ? '下一步' : '完成' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, onMounted, computed } from 'vue'
import PageTitle from '@/components/PageTitle/index.vue'
import { addDevice, updateDevice, getDeviceDetail } from '@/api/device'
import { listHouseTree } from '@/api/house'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object,
    default: () => null
  }
})

const emit = defineEmits(['update:visible', 'success'])

const dialogVisible = ref(props.visible)
const currentStep = ref(1)
const baseFormRef = ref(null)

// 基础信息表单
const baseForm = reactive({
  deviceName: '',
  deviceType: '',
  deviceModel: '',
  imei: '',
  houseId: '',
  status: '0'
})

// 阈值列表
const thresholdList = ref([
  { dimension: '位移值', level: '一级', value: '', description: '' },
  { dimension: '位移值', level: '二级', value: '', description: '' },
  { dimension: '位移值', level: '三级', value: '', description: '' }
])

const houseOptions = ref([])
const houseTreeData = ref([])

const getHouseOptions = async() => {
  try {
    const res = await listHouseTree({ pageNum: 1, pageSize: 1000, status: '0' })
    if (res) {
      const data = res || []
      houseOptions.value = extractHouseOptions(data)
      houseTreeData.value = transformToTreeData(data)
      console.log('接口返回数据:', data)
      console.log('转换后的树形数据:', houseTreeData.value)
    }
  } catch (e) {
    console.error('获取房屋数据失败:', e)
    houseOptions.value = []
    houseTreeData.value = []
  }
}

// 提取所有房屋选项为扁平结构
const extractHouseOptions = (data) => {
  let houses = []

  const extractHouses = (items) => {
    if (!items) return

    items.forEach(item => {
      // 添加当前节点的房屋
      if (item.house && Array.isArray(item.house)) {
        houses = houses.concat(item.house)
      }

      // 递归处理子节点
      if (item.children && Array.isArray(item.children)) {
        extractHouses(item.children)
      }
    })
  }

  extractHouses(data)
  return houses
}

// 转换为树形结构
const transformToTreeData = (data) => {
  const transformNode = (item) => {
    // 创建组织节点
    const node = {
      id: item.id,
      label: item.name,
      value: `org_${item.id}`, // 使用前缀区分组织和房屋
      children: []
    }

    // 添加房屋作为子节点
    if (item.house && Array.isArray(item.house)) {
      item.house.forEach(house => {
        node.children.push({
          id: house.houseId,
          label: house.houseAddress,
          value: house.houseId,
          isLeaf: true
        })
      })
    }

    // 处理子组织
    if (item.children && Array.isArray(item.children)) {
      const childNodes = item.children.map(transformNode)
      node.children = [...node.children, ...childNodes]
    }

    return node
  }

  return data.map(transformNode)
}

onMounted(() => {
  getHouseOptions()
})

// 表单验证规则
const baseRules = {
  deviceName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
  deviceType: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
  deviceModel: [{ required: true, message: '请选择设备型号', trigger: 'change' }],
  imei: [{ required: true, message: '请输入设备序列号', trigger: 'blur' }],
  houseId: [{ required: true, message: '请选择安装地址', trigger: 'change' }]
}

// 初始化编辑数据
const initEditData = async() => {
  if (props.editData && props.editData.deviceId) {
    // 编辑时，调用详情接口
    try {
      const res = await getDeviceDetail(props.editData.deviceId)
      if (res.code === 200 && res.data) {
        // 填充基础信息
        Object.keys(baseForm).forEach(key => {
          if (res.data[key] !== undefined) {
            baseForm[key] = res.data[key]
          }
        })

        console.log('设备状态after:', baseForm.status)

        // 确保保存deviceId
        baseForm.deviceId = props.editData.deviceId
        // 填充阈值信息
        if (Array.isArray(res.data.deviceThresholds)) {
          thresholdList.value = res.data.deviceThresholds
        }
      }
    } catch (e) {
      ElMessage.error('获取设备详情失败')
    }
  } else {
    // 新增时，清空表单
    Object.keys(baseForm).forEach(key => {
      baseForm[key] = key === 'status' ? '0' : ''
    })
    thresholdList.value = [
      { dimension: '位移值', level: '一级', value: '', description: '' },
      { dimension: '位移值', level: '二级', value: '', description: '' },
      { dimension: '位移值', level: '三级', value: '', description: '' }
    ]
  }
}

// 重置表单
const resetForm = () => {
  baseFormRef.value?.resetFields()
  Object.keys(baseForm).forEach(key => {
    if (key === 'status') {
      baseForm[key] = '0'
    } else {
      baseForm[key] = ''
    }
  })
  thresholdList.value = [
    { dimension: '位移值', level: '一级', value: '', description: '' },
    { dimension: '位移值', level: '二级', value: '', description: '' },
    { dimension: '位移值', level: '三级', value: '', description: '' }
  ]
}

// 关闭弹窗
const handleClose = () => {
  currentStep.value = 1
  resetForm()
  emit('update:visible', false)
}

// 上一步
const handlePrev = () => {
  currentStep.value = 1
}

// 下一步或完成
const handleNext = async() => {
  if (currentStep.value === 1) {
    // 检查是否选择了组织而不是房屋
    if (baseForm.houseId && String(baseForm.houseId).startsWith('org_')) {
      ElMessage.warning('请选择具体的房屋地址，而不是组织')
      return
    }

    await baseFormRef.value.validate()
    if (props.editData && props.editData.deviceId) {
      // 编辑时，第一步直接跳到第二步
      currentStep.value = 2
      return
    }
    try {
      // 新增时，第一步调用addDevice
      const res = await addDevice({ ...baseForm })
      if (res.code === 200) {
        if (res.data && Array.isArray(res.data.deviceThresholds)) {
          thresholdList.value = res.data.deviceThresholds
        } else if (res.deviceThresholds && Array.isArray(res.deviceThresholds)) {
          thresholdList.value = res.deviceThresholds
        }
        baseForm.deviceId = res.deviceId || (res.data && res.data.deviceId)
        currentStep.value = 2
      } else {
        ElMessage.error(res.msg || '新增设备失败')
      }
    } catch (error) {
      console.error(error)
      ElMessage.error('新增设备失败')
    }
  } else {
    // 第二步：提交阈值，无论新增还是编辑都用put
    try {
      const formData = {
        ...baseForm,
        deviceThresholds: thresholdList.value
      }

      // 确保编辑时传递deviceId
      if (props.editData && props.editData.deviceId) {
        formData.deviceId = props.editData.deviceId
      }

      console.log('提交前的表单数据:', formData)
      console.log('提交前的status值:', formData.status)

      const res = await updateDevice(formData)
      if (res.code === 200) {
        ElMessage.success('保存成功')
        emit('success', formData)
        handleClose()
      } else {
        ElMessage.error(res.msg || '保存失败')
      }
    } catch (error) {
      console.error(error)
      ElMessage.error('保存失败')
    }
  }
}

// 监听visible变化
watch(
  () => props.visible,
  async(val) => {
    dialogVisible.value = val
    if (val) {
      await initEditData()
    }
  }
)

// 监听dialogVisible变化
watch(
  () => dialogVisible.value,
  (val) => {
    emit('update:visible', val)
  }
)

// 按 thresholdTypeText 分组
const groupedThresholds = computed(() => {
  const map = {}
  thresholdList.value.forEach(item => {
    if (!map[item.thresholdTypeText]) map[item.thresholdTypeText] = []
    map[item.thresholdTypeText].push(item)
  })
  return map
})
</script>

<style scoped>
.add-device-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-dialog) {

  position: relative;
  top: 50%;
  transform: translateY(-50%);
}

.add-device-dialog :deep(.el-dialog__body) {
  padding: 0 20px;
}

.threshold-container {
  height: 550px;
  overflow-y: auto;
  scrollbar-width: none;  /* Firefox */
  -ms-overflow-style: none;  /* IE and Edge */
}

.threshold-container::-webkit-scrollbar {
  display: none;  /* Chrome, Safari, Opera */
}

.dialog-footer {
  padding: 20px 0;
  text-align: right;
}

.threshold-table {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.threshold-table) {
  /* 确保表格的边框也是圆角 */
  border-radius: 8px;
}

:deep(.threshold-table .el-table__inner-wrapper) {
  /* 确保内部包装器也是圆角 */
  border-radius: 8px;
}
</style>
