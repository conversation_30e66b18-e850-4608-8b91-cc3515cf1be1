<template>
  <el-dialog
    v-model="dialogVisible"
    title="设备详情"
    width="960px"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    style="margin-top: 25vh;"
  >
    <page-title title="基础信息">
      <el-descriptions :column="2" label-width="80px">
        <el-descriptions-item label="设备名称" class="desc-item">{{ detail.deviceName }}</el-descriptions-item>
        <el-descriptions-item label="设备类型" class="desc-item">{{ detail.deviceTypeText }}</el-descriptions-item>
        <el-descriptions-item label="设备型号" class="desc-item">{{ detail.deviceModel }}</el-descriptions-item>
        <el-descriptions-item label="设备序列号" class="desc-item">{{ detail.imei }}</el-descriptions-item>
        <el-descriptions-item label="安装地址" class="desc-item">{{ detail.houseAddress }}</el-descriptions-item>
        <el-descriptions-item label="启用状态" class="desc-item">
          <el-switch
            v-model="detail.status"
            active-value="0"
            inactive-value="1"
            disabled
          />
        </el-descriptions-item>
      </el-descriptions>
    </page-title>

    <page-title title="设备告警阈值">
      <el-tabs v-model="activeTab" class="device-tabs" type="card">
        <el-tab-pane
          v-for="typeText in tabList"
          :key="typeText"
          :label="typeText"
          :name="typeText"
        >
          <el-table :data="groupedThresholds[typeText]" border class="threshold-table">
            <el-table-column
              label="告警等级"
              prop="alarmLevelText"
              align="center"
            />
            <el-table-column
              label="指标"
              prop="dimensionTypeText"
              align="center"
            />
            <el-table-column
              label="阈值"
              prop="thresholdNum"
              align="center"
            />
            <el-table-column
              label="说明"
              prop="remark"
              align="center"
            />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </page-title>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" style="background-color: #165DFF; border: none" @click="handleEdit">编辑</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import PageTitle from '@/components/PageTitle/index.vue'
import { getDeviceDetail } from '@/api/device'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  deviceId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'edit'])

const dialogVisible = ref(props.visible)
const activeTab = ref('')
const detail = reactive({
  deviceName: '',
  deviceType: '',
  deviceModel: '',
  deviceId: '',
  location: '',
  status: '0',
  deviceThresholds: [],
  houseAddress: ''
})

// 分组：{ thresholdTypeText: [ ... ] }
const groupedThresholds = computed(() => {
  const map = {}
  if (Array.isArray(detail.deviceThresholds)) {
    detail.deviceThresholds.forEach(item => {
      if (!map[item.thresholdTypeText]) map[item.thresholdTypeText] = []
      map[item.thresholdTypeText].push(item)
    })
  }
  return map
})

const tabList = computed(() => Object.keys(groupedThresholds.value))

// 获取设备详情
const getDetail = async() => {
  try {
    const res = await getDeviceDetail(props.deviceId)
    if (res.code === 200) {
      Object.assign(detail, res.data)
      // 默认选中第一个tab
      if (Array.isArray(res.data.deviceThresholds) && res.data.deviceThresholds.length > 0) {
        const firstType = res.data.deviceThresholds[0].thresholdTypeText
        activeTab.value = firstType
      }
    } else {
      ElMessage.error(res.message || '获取详情失败')
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取详情失败')
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 编辑按钮点击
const handleEdit = () => {
  emit('edit', detail)
  handleClose()
}

// 监听visible变化
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
    if (val && props.deviceId) {
      getDetail()
    }
  }
)

// 监听dialogVisible变化
watch(
  () => dialogVisible.value,
  (val) => {
    emit('update:visible', val)
  }
)
</script>

<style scoped>
.device-tabs :deep(.el-tabs__header) {
  margin-bottom: 16px;
  border-bottom: none;
}

.device-tabs :deep(.el-tabs__nav) {
  border: none;
}

.device-tabs :deep(.el-tabs__item) {
  border: none !important;
  height: 32px;
  line-height: 32px;
  padding: 0 16px !important;
  border-radius: 16px !important;
  transition: all 0.3s;
  color: #1D2129;
}

.device-tabs :deep(.el-tabs__item.is-active) {
  color: #165DFF;
  background-color: #E8F3FF;
}

.device-tabs :deep(.el-tabs__nav-wrap::after) {
  display: none;
}

.threshold-table {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-descriptions) {
  padding: 0;
}

:deep(.el-descriptions__header) {
  display: none;
}

:deep(.desc-item) {
  padding: 12px 0;
  margin-right: 32px;
}

:deep(.el-descriptions__label) {
  width: 100px;
  color: #86909C;
  text-align: right;
}

:deep(.desc-item .el-descriptions__content) {
  color: #1D2129;
}

.dialog-footer {
  padding: 20px 0;
  text-align: right;
}
</style>
