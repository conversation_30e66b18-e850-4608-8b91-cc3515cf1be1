<template>
  <div class="app-container">
    <page-title title="筛选条件">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <div class="form-content">
          <div class="form-items">
            <el-form-item label="设备名称">
              <el-input v-model="queryParams.deviceName" placeholder="请输入" style="width: 373px;" />
            </el-form-item>
            <el-form-item label="启用状态">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择"
                style="width: 373px;"
              >
                <el-option label="启用" value="0" />
                <el-option label="禁用" value="1" />
              </el-select>
            </el-form-item>
          </div>
          <div class="form-buttons">
            <el-button @click="resetQuery">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button type="primary" style="background-color: #165DFF; border: none" @click="handleQuery">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
          </div>
        </div>
      </el-form>
    </page-title>

    <page-title title="设备列表">
      <div class="table-operations">
        <el-button type="primary" style="background-color: #165DFF; border: none" @click="showAddDialog">
          <el-icon><Plus /></el-icon>
          新增设备
        </el-button>
      </div>
      <el-table
        v-loading="loading"
        :data="deviceList"
        style="width: 100%"
        border
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="设备序列号" prop="imei" align="center" />
        <el-table-column label="设备名称" prop="deviceName" align="center" />
        <el-table-column label="设备类型" prop="deviceTypeText" align="center" />
        <el-table-column label="设备型号" prop="deviceModel" align="center" />
        <el-table-column label="属地单位" prop="localUnitText" align="center" />
        <el-table-column label="安装位置" prop="houseAddress" align="center" />
        <el-table-column label="安装时间" prop="createTime" align="center" />
        <el-table-column label="启用状态" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              active-value="0"
              inactive-value="1"
              @change="handleSwitchChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150">
          <template #default="scope">
            <el-button type="text" style="color: #165DFF;" @click="handleView(scope.row)">查看</el-button>
            <el-button type="text" style="color: #165DFF;" @click="handleEdit(scope.row)">编辑</el-button>
            <!-- <el-button type="text" style="color: #f56c6c;" @click="handleDelete(scope.row)">删除</el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 30, 40]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </page-title>

    <!-- 新增设备弹窗 -->
    <add-device
      v-model:visible="addDialogVisible"
      :edit-data="editData"
      @success="handleAddSuccess"
    />

    <!-- 设备详情弹窗 -->
    <device-detail
      v-model:visible="detailDialogVisible"
      :device-id="currentDeviceId"
      @edit="handleDetailEdit"
    />
  </div>
</template>

<script setup name="Device">
import { ref, reactive, onMounted } from 'vue'
import PageTitle from '@/components/PageTitle/index.vue'
import { Search, Refresh, Plus } from '@element-plus/icons-vue'
import AddDevice from './components/AddDevice.vue'
import DeviceDetail from './components/DeviceDetail.vue'
import { getDeviceList, deleteDevice, switchDevice } from '@/api/device'
import { ElMessage, ElMessageBox } from 'element-plus'

const queryParams = reactive({
  deviceName: '',
  status: '',
  pageNum: 1,
  pageSize: 10
})

const deviceList = ref([])
const total = ref(0)
const loading = ref(false)

const addDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const currentDeviceId = ref('')
const editData = ref(null)

const handleQuery = async() => {
  loading.value = true
  try {
    const res = await getDeviceList(queryParams)
    if (res.code === 200) {
      deviceList.value = res.rows
      total.value = res.total
    } else {
      ElMessage.error(res.msg || '获取数据失败')
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const resetQuery = () => {
  queryParams.deviceName = ''
  queryParams.status = ''
  handleQuery()
}

const showAddDialog = () => {
  editData.value = null
  addDialogVisible.value = true
}

const handleAddSuccess = (data) => {
  // TODO: 调用新增或编辑接口
  console.log('提交的数据:', data)
  // 刷新列表
  handleQuery()
}

const handleView = (row) => {
  currentDeviceId.value = row.deviceId
  detailDialogVisible.value = true
}

const handleDetailEdit = (detail) => {
  detailDialogVisible.value = false
  editData.value = detail
  addDialogVisible.value = true
}

const handleEdit = (row) => {
  console.log('编辑前的行数据:', row)
  editData.value = { ...row, deviceId: row.deviceId || row.id }
  console.log('编辑数据:', editData.value)
  addDialogVisible.value = true
}

const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除设备 "${row.deviceName}" 吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async() => {
    try {
      const res = await deleteDevice(row.deviceId)
      if (res.code === 200) {
        ElMessage.success('删除成功')
        handleQuery()
      } else {
        ElMessage.error(res.message || '删除失败')
      }
    } catch (error) {
      console.error(error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    // 用户取消删除操作
  })
}

const handleSizeChange = (val) => {
  queryParams.pageSize = val
  queryParams.pageNum = 1 // 切换每页显示数量时，重置到第一页
  handleQuery()
}

const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  handleQuery()
}

const handleSwitchChange = async(row) => {
  try {
    await switchDevice(row.deviceId || row.id)
    ElMessage.success('状态切换成功')
    handleQuery()
  } catch (e) {
    ElMessage.error('状态切换失败')
    handleQuery()
  }
}

onMounted(() => {
  handleQuery()
})
</script>

<style scoped>
.form-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.form-items {
  display: flex;
  gap: 20px;
}

.form-buttons {
  display: flex;
  gap: 12px;
}

:deep(.el-button .el-icon) {
  margin-right: 4px;
  vertical-align: middle;
}

.table-operations {
  margin-bottom: 16px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
