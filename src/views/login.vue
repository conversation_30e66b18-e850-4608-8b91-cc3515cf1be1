<!-- eslint-disable vue/valid-attribute-name -->
<template>
  <div
    class="login-container"
  >
    <div class="logo-container">
      <img src="../assets/images/logo.svg" alt="Logo" class="logo">
      <div class="logo-text-container">
        <div class="logo-text">默宁智能监测云平台</div>
        <div class="logo-text-en">MORNING IOT MIDDLE PLATFORM</div>
      </div>
    </div>
    <el-form
      ref="loginRef"
      :model="loginForm"
      :rules="loginRules"
      class="login-form"
    >
      <h3 class="title">
        欢迎登录
      </h3>
      <h4 class="subtitle">
        默宁智能监测云平台
      </h4>
      <el-form-item prop="tel">
        <el-input
          v-model="loginForm.tel"
          type="text"
          size="large"
          auto-complete="off"
          placeholder="请输入账号"
          maxlength="11"
        >
          <template #prefix>
            <svg-icon
              icon-class="phone"
              class="el-input__icon input-icon"
            />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="code">
        <div class="code-input-container">
          <el-input
            v-model="loginForm.code"
            type="text"
            size="large"
            auto-complete="off"
            placeholder="请输入验证码"
            maxlength="6"
            class="code-input"
            @keyup.enter="handleLogin"
          >
            <template #prefix>
              <svg-icon
                icon-class="validCode"
                class="el-input__icon input-icon"
              />
            </template>
          </el-input>
          <el-button
            plain
            class="send-code-btn"
            :loading="smsLoading"
            @click="getSmsCode1"
          >
            {{ smsDone ? `${smsTime}秒后重试` : smsLoading ? '验证码获取中' : '发送验证码' }}
          </el-button>
        </div>
      </el-form-item>
      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="large"
          type="primary"
          class="login-button"
          @click.prevent="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
      </el-form-item>
      <el-form-item prop="ifchecked" class="agreement-item">
        <div class="agreement-container">
          <el-checkbox
            v-model="loginForm.ifchecked"
            class="agreement-checkbox"
            label=""
            size="mini"
          />
          <div class="agreement-text">我已阅读并同意 <span class="agreement-link" @click="handleDesclaimer">《协议条款》</span></div>
        </div>
      </el-form-item>
      <div
        v-if="register"
        style="text-align: center;"
      >
        <router-link
          class="link-type"
          :to="'/register'"
        >
          立即注册
        </router-link>
      </div>
    </el-form>
    <el-dialog
      v-model="centerDialogVisible"
      title=""
      width="50%"
      center
    >
      <div v-html="content" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleConfirmRead">已阅读</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加备案号 -->
    <div class="icp-footer">
      渝ICP备2025063025号-1
    </div>
  </div>
</template>

<script setup>
import { getSmsCode, getDisclaimerInfo } from '@/api/login'
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()
const router = useRouter()
const { proxy } = getCurrentInstance()
const centerDialogVisible = ref(false)
const content = ref('')

const loginForm = ref({
  tel: '',
  code: '',
  ifchecked: false
})

const loginRules = {
  tel: [
    { required: true, trigger: 'blur', message: '请输入您的手机号' },
    { pattern: /^1\d{10}$/, trigger: 'blur', message: '请输入正确的手机号' }
  ],
  code: [{ required: true, trigger: 'change', message: '请输入验证码' }],
  ifchecked: [
    { required: true, message: '请阅读并同意协议', trigger: 'blur' }
  ]
}

const loading = ref(false)
// 手机验证码获取请求中
const smsLoading = ref(false)
// 验证码获取完成
const smsDone = ref(false)
const smsTime = ref(120)
// 注册开关
const register = ref(false)
const redirect = ref(undefined)

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid && loginForm.value.ifchecked === true) {
      loading.value = true
      userStore.LoginTel(loginForm.value).then(() => {
        router.push({ path: redirect.value || '/' })
      }).catch(() => {
        loading.value = false
      })
    } else {
      proxy.$modal.msgError('请输入正确手机号和验证码并勾选同意协议条款')
    }
  })
}

function handleConfirmRead() {
  centerDialogVisible.value = false
  loginForm.value.ifchecked = true
}

function handleDesclaimer() {
  centerDialogVisible.value = true
  getClaimer()
}

// 获取验证码
function getSmsCode1() {
  getSmsCode({
    phone: loginForm.value.tel
  }).then(() => {
    smsLoading.value = true
    setTimeout(() => {
      smsDone.value = true
      smsCount()
    }, 1000)
  })
}

// 验证码倒计时
function smsCount() {
  if (smsTime.value > 0) {
    setTimeout(() => {
      smsTime.value--
      smsCount()
    }, 1000)
  } else {
    smsTime.value = 120
    smsLoading.value = false
    smsDone.value = false
  }
}

function getClaimer() {
  getDisclaimerInfo().then((res) => {
    content.value = res.data.content
  })
}
</script>

<style lang='scss' scoped>
.login-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 100%;
  width: 100%;
  background-image: url("../assets/images/login-background.png");
  background-size: cover;
  position: relative;
  padding-right: 200px;
}

.logo-container {
  position: absolute;
  top: 48px;
  left: 48px;
  display: flex;
  align-items: center;
}

.logo {
  width: 64px;
  height: 64px;
  margin-right: 10px;
}

.logo-text-container {
  display: flex;
  flex-direction: column;
}

.logo-text {
  color: #4E5969;
  font-size: 32px;
  line-height: 100%;
  font-weight: 700;
  font-family: 'Alimama ShuHeiTi', sans-serif;
}

.logo-text-en {
  margin-top: 4px;
  color: #4E5969;
  font-size: 14px;
  font-weight: 400;
  font-family: 'Hedvig Letters Sans', sans-serif;
  text-transform: uppercase;
  line-height: 100%;
  letter-spacing: 0;
  text-align: left;
}

.title {
  margin: 0 0 10px 0;
  text-align: left;
  font-size: 36px;
  font-weight: 600;
  color: #333;
  line-height: 44px;
  letter-spacing: 0;
}

.subtitle {
  margin: 0 0 40px 0;
  text-align: left;
  font-size: 36px;
  color: #333;
  font-weight: 600;
  line-height: 44px;
}

.login-form {
  border-radius: 8px;
  background: #ffffff;
  width: 496px;
  height: 556px;
  padding: 50px 40px 40px 40px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .el-input {
    height: 50px;
    margin-bottom: 0;

    input {
      height: 50px;
      font-size: 16px;
    }
  }

  .el-form-item {
    margin-bottom: 20px;
  }

  .input-icon {
    height: 50px;
    width: 16px;
    margin-left: 0px;
  }
}

.agreement-item {
  margin-top: 10px;
  margin-bottom: 0;
}

.agreement-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.agreement-checkbox {
  margin-right: 5px;
}

.agreement-text {
  font-size: 14px;
  color: #999;
}

.agreement-link {
  color: #1890ff;
  cursor: pointer;
}

.code-input-container {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.code-input {
  width: calc(100% - 120px);
}

.send-code-btn {
  height: 50px;
  width: 120px;
  white-space: nowrap;
  border-radius: 4px;
  padding: 0;
  border: 1px solid #DCDCDC;
  color: #1A1A1A;
}

.login-button {
  width: 100%;
  border-radius: 4px;
  background-color: #165DFF;
  height: 50px;
  font-size: 18px;
  font-weight: 400;
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 40px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 40px;
  padding-left: 12px;
}

/* 备案号样式 */
.icp-footer {
  position: absolute;
  bottom: 20px;
  left: 0;
  width: 100%;
  text-align: center;
  color: #bbb;
  font-size: 14px;
}
</style>

