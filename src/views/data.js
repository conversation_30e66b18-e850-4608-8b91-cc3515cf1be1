export const searchData = [
  {
    prop: 'configName',
    label: '参数名称',
    defaultValue: 'a',
    type: 'input',
    clearable: true
  },
  {
    prop: 'configKey',
    label: '参数键名',
    defaultValue: undefined,
    type: 'input',
    clearable: true
  },
  {
    prop: 'configType',
    label: '系统内置',
    defaultValue: undefined,
    type: 'select',
    clearable: true
  },
  {
    prop: 'createDate',
    label: '创建日期',
    defaultValue: undefined,
    type: 'date',
    range: true,
    clearable: true
  },
  {
    prop: 'createTime',
    label: '创建时间',
    defaultValue: undefined,
    type: 'datetime',
    range: true,
    clearable: true
  }
]

export const tableColumns = [
  { align: 'center', type: 'selection' },
  { label: '序号', prop: 'xh', align: 'center' },
  { label: '所属组织', prop: 'orgName', align: 'center' },
  { label: '所属区域', prop: 'regionTipText', align: 'center' },
  { label: '姓名', prop: 'userName', align: 'center' },
  { label: '身份证号', prop: 'idCard', align: 'center' },
  { label: '性别', prop: 'genderTipText', align: 'center' },
  { label: '操作', slotName: 'operate', fixed: 'right', align: 'center' }
]

export function testList(params) {
  console.log(`output->params`, params)
  if (params.configName === 'a') {
    return Promise.resolve({
      data: [{
        xh: 'xh1',
        orgName: 'orgName1',
        regionTipText: 'regionTipText1',
        userName: 'userName1',
        idCard: 'idCard1',
        genderTipText: 'genderTipText1'
      }, {
        xh: 'xh2',
        orgName: 'orgName2',
        regionTipText: 'regionTipText2',
        userName: 'userName2',
        idCard: 'idCard2',
        genderTipText: 'genderTipText2'
      }]
    })
  }
  return Promise.resolve({})
}
/**
 * upperTitle 上层标题，若传递upper-title插槽则展示自定义样式，否则使用默认样式
 *  @example  <template #upper-title="{ title }">
                <div style="color: #f00;">{{ title }}</div>
              </template>
 * label 显示文字
 * type 显示类型，可选值：input、select、radio、checkbox、date、datetime、pic、editor
 * defaultValue 初始值，默认undefined，注意[range为true的日期时间、checkbox、多张图片、文件上传]类型为array
 * rules 校验规则 [{required: true, message: '请输入标题', trigger: 'blur'}]
 * options 选项，type为select、radio、checkbox时填写，或可通过optionsObj传递，必须保证组件内部能获取到值
 *         格式：[{labe: '选项1', value: 1}]
 * rowProps 布局属性，如：span、offset、push、pull、xs、sm、md
 * elProps 组件属性，如：placeholder、clearable、format、limit、valueType
 * formItemProps 表单属性，如：labelWidth、size
 */
export const formConfig = [
  {
    upperTitle: '基础信息',
    label: '标题',
    type: 'input',
    prop: 'title',
    defaultValue: 'This is title',
    rules: [
      {
        required: true,
        message: '请输入标题',
        trigger: 'blur'
      }
    ],
    rowProps: {
      span: 24
    }
  },
  {
    upperTitle: '开户行信息',
    slotName: 'bank-info',
    prop: 'bankInfo'
  },
  {
    label: '附件',
    type: 'file',
    prop: 'file',
    rules: [
      {
        required: false,
        message: '请上传附件',
        trigger: 'blur'
      }
    ],
    // defaultValue: [{
    //   name: '附件1.jpg',
    //   status: 'uploading',
    //   url: 'http://219.152.95.18:8077/profile/upload/2024/07/25/厕所_20230302154823A717_20240725151447A015.jpg'
    // }, {
    //   name: '附件2.jpg',
    //   status: 'uploading',
    //   url: 'http://219.152.95.18:8077/profile/upload/2024/07/25/厕所_20230302154823A717_20240725151447A015.jpg'
    // }],
    rowProps: {
      span: 12
    },
    elProps: {
      fileSize: 50,
      fileType: ['png', 'jpg', 'jpeg'],
      limit: 2
    }
  },
  {
    label: '排序',
    type: 'number',
    prop: 'sort',
    defaultValue: 0,
    span: 12,
    elProps: {
      min: 2,
      max: 5,
      controlsPosition: 'right'
    }
  },
  {
    label: '类型',
    type: 'select',
    prop: 'infoType'
  },
  {
    label: '置顶',
    type: 'switch',
    prop: 'top',
    defaultValue: false,
    elProps: {
      activeText: 'yes',
      inactiveText: 'no'
    }
  },
  // {
  //   label: '性别',
  //   type: 'radio',
  //   prop: 'gender',
  //   defaultValue: 'female',
  //   options: [
  //     {
  //       label: '男',
  //       value: 'male',
  //       elProps: {
  //         disabled: true
  //       }
  //     },
  //     {
  //       label: '女',
  //       value: 'female'
  //     }
  //   ]
  // },
  // {
  //   label: '喜欢的运动',
  //   type: 'checkbox',
  //   prop: 'sport',
  //   options: [
  //     {
  //       label: '篮球',
  //       value: 'basketball',
  //       elProps: {
  //         disabled: true
  //       }
  //     },
  //     {
  //       label: '足球',
  //       value: 'football'
  //     },
  //     {
  //       label: '跑步',
  //       value: 'running'
  //     }
  //   ],
  //   formItemProps: {
  //     labelWidth: '120px'
  //   }
  // },
  // {
  //   label: '出生日期',
  //   type: 'date',
  //   range: false,
  //   prop: 'birthday',
  //   defaultValue: '2023-01-01',
  //   elProps: {}
  // },
  // {
  //   label: '合同日期',
  //   type: 'date',
  //   range: true,
  //   prop: 'dateRange',
  //   elProps: {
  //     startPlaceholder: '合同开始日期',
  //     endPlaceholder: '合同结束日期'
  //   }
  // },
  // {
  //   label: '单个时间',
  //   type: 'datetime',
  //   range: false,
  //   prop: 'datetime',
  //   defaultValue: '2023-01-01 12:23:43',
  //   elProps: {}
  // },
  // {
  //   label: '多个时间',
  //   type: 'datetime',
  //   range: true,
  //   prop: 'datetimeRange',
  //   elProps: {
  //     startPlaceholder: '合同开始时间',
  //     endPlaceholder: '合同结束时间'
  //   }
  // },
  // {
  //   label: '图片上传',
  //   type: 'pic',
  //   prop: 'pic',
  //   defaultValue: 'http://219.152.95.18:8077/profile/upload/2024/07/25/路飞111_20240725140741A011.jpg',
  //   rules: [
  //     {
  //       required: true,
  //       message: '请上传图片',
  //       trigger: 'blur'
  //     }
  //   ],
  //   rowProps: {
  //     span: 24
  //   },
  //   elProps: {
  //     limit: 1,
  //     fileSize: 50,
  //     valueType: 'string'
  //   }
  // },
  {
    upperTitle: '图片上传',
    label: '多张图片上传',
    type: 'pic',
    prop: 'pics',
    defaultValue: ['http://219.152.95.18:8077/profile/upload/2024/07/25/路飞111_20240725140741A011.jpg', 'http://219.152.95.18:8077/profile/upload/2024/07/25/路飞111_20240725140741A011.jpg'],
    rules: [
      {
        required: true,
        message: '请上传图片',
        trigger: 'blur'
      }
    ],
    rowProps: {
      span: 24
    },
    elProps: {
      limit: 2,
      fileSize: 50,
      valueType: 'array'
    }
  },
  // {
  //   lable: '富文本',
  //   type: 'editor',
  //   prop: 'content',
  //   defaultValue: '',
  //   elProps: {
  //     height: 300
  //   },
  //   rowProps: {
  //     span: 24
  //   }
  // }
  {
    label: '选择人员',
    slotName: 'select-user',
    prop: 'userList'
  }
]
