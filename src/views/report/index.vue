<template>
  <div class="app-container home">
    <el-dialog
      v-model="dialogVisible"
      title="报告下载中"
      :close-on-click-modal="false"
      :show-close="false"
      width="1200px"
    >
      <div style="margin-bottom: 16px;">
        <el-progress :percentage="progress" :text-inside="true" :stroke-width="20" />
      </div>
      <el-link
        :underline="false"
        type="primary"
        :disabled="downloading"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const dialogVisible = ref(true)
const progress = ref(0)
const downloading = ref(false)

// 假设你的word文档下载地址
const reportUrl = 'https://www.cqmorningtec.com/profile/upload/结构安全智能监测报告.docx'

const downloadReport = async() => {
  downloading.value = true
  progress.value = 10

  // 使用fetch实现带进度的下载
  const response = await fetch(reportUrl)
  const reader = response.body.getReader()
  const contentLength = +response.headers.get('Content-Length')
  let receivedLength = 0
  const chunks = []

  while (true) {
    const { done, value } = await reader.read()
    if (done) break
    chunks.push(value)
    receivedLength += value.length
    progress.value = Math.floor((receivedLength / contentLength) * 100)
  }

  // 合并二进制流
  const blob = new Blob(chunks, { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = '结构安全智能监测报告.docx'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  progress.value = 100
  downloading.value = false
  dialogVisible.value = false // 下载完成后关闭弹窗
}
onMounted(() => {
  setTimeout(() => {
    downloadReport()
  }, 1000)
})
</script>

<style scoped lang="scss">
/* ...原有样式... */
:deep(.el-dialog) {
  top: 25% !important;
  transform: translateY(-50%) !important;
}
.home {
  width: 100%;
  height: 100%;
  background-color: #fff;
}
.title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 240px;
}
</style>
