<template>
  <div class="app-container">
    <page-title title="筛选条件">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <div class="form-content">
          <div class="form-items">
            <el-form-item label="设备名称">
              <el-input v-model="queryParams.deviceName" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="处理状态">
              <el-select v-model="queryParams.handleStatus" placeholder="请选择" style="width: 200px;">
                <el-option label="已处理" value="true" />
                <el-option label="未处理" value="false" />
              </el-select>
            </el-form-item>
          </div>
          <div class="form-buttons">
            <el-button @click="resetQuery">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button type="primary" style="background-color: #165DFF; border: none" @click="handleQuery">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
          </div>
        </div>
      </el-form>
    </page-title>

    <page-title title="告警列表">
      <el-table
        v-loading="loading"
        :data="alarmList"
        style="width: 100%"
        border
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          label="告警内容"
          prop="alarmContext"
          width="250"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column label="告警等级" prop="alarmLevelText" align="center" />
        <el-table-column label="设备名称" prop="deviceName" align="center" />
        <el-table-column label="处理状态" prop="handleStatus" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.handleStatus === true ? 'success' : 'warning'">
              {{ scope.row.handleStatus === true ? '已处理' : '未处理' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="属地单位" prop="localUnitText" align="center" />
        <el-table-column label="告警位置" prop="houseAddress" align="center" />
        <el-table-column label="责任人" prop="responsiblePerson" align="center" />
        <el-table-column label="告警时间" prop="alarmTime" align="center" />
        <el-table-column label="操作" align="center" width="150">
          <template #default="scope">
            <el-button type="text" style="color: #165DFF;" @click="handleView(scope.row)">查看</el-button>
            <el-button
              v-if="scope.row.handleStatus === false"
              type="text"
              style="color: #165DFF;"
              @click="handleProcess(scope.row)"
            >处理</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 30, 40]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </page-title>

    <!-- 告警详情弹窗 -->
    <alarm-detail
      v-model:visible="detailDialogVisible"
      :log-id="currentAlarmId"
    />

    <!-- 处理告警弹窗 -->
    <alarm-process
      v-model:visible="processDialogVisible"
      :log-id="currentAlarmId"
      @success="handleProcessSuccess"
    />
  </div>
</template>

<script setup name="Alarm">
import { ref, reactive, onMounted, onBeforeMount } from 'vue'
import PageTitle from '@/components/PageTitle/index.vue'
import { Search, Refresh } from '@element-plus/icons-vue'
import AlarmDetail from './components/AlarmDetail.vue'
import AlarmProcess from './components/AlarmProcess.vue'
import { getAlarmList } from '@/api/alarm'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'

// 添加路由实例，用于获取查询参数
const route = useRoute()

const queryParams = reactive({
  deviceName: '',
  status: '',
  handleStatus: '',
  logId: '',
  pageNum: 1,
  pageSize: 10
})

const alarmList = ref([])
const total = ref(0)
const loading = ref(false)

const detailDialogVisible = ref(false)
const processDialogVisible = ref(false)
const currentAlarmId = ref('')

// 检查URL参数并设置查询条件
const checkRouteParams = () => {
  const { deviceName, handleStatus, logId } = route.query

  if (deviceName) {
    queryParams.deviceName = deviceName
  }

  if (handleStatus !== undefined) {
    queryParams.handleStatus = handleStatus
  }
  if (logId) {
    queryParams.logId = logId
  }
}

const handleQuery = async() => {
  loading.value = true
  try {
    const res = await getAlarmList(queryParams)
    if (res.code === 200) {
      alarmList.value = res.rows
      total.value = res.total
    } else {
      ElMessage.error(res.message || '获取数据失败')
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const resetQuery = () => {
  queryParams.deviceName = ''
  queryParams.status = ''
  queryParams.handleStatus = ''
  handleQuery()
}

const handleView = (row) => {
  currentAlarmId.value = row.logId
  detailDialogVisible.value = true
}

const handleProcess = (row) => {
  currentAlarmId.value = row.logId
  processDialogVisible.value = true
}

const handleProcessSuccess = () => {
  handleQuery()
}

const handleSizeChange = (val) => {
  queryParams.pageSize = val
  queryParams.pageNum = 1
  handleQuery()
}

const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  handleQuery()
}

onBeforeMount(() => {
  // 在组件挂载前检查URL参数
  checkRouteParams()
})

onMounted(() => {
  // 使用可能从URL获取的参数进行查询
  handleQuery()
})
</script>

<style scoped>
.form-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.form-items {
  display: flex;
  gap: 20px;
}

.form-buttons {
  display: flex;
  gap: 12px;
}

:deep(.el-button .el-icon) {
  margin-right: 4px;
  vertical-align: middle;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
