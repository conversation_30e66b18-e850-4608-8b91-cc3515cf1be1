<template>
  <el-dialog
    v-model="dialogVisible"
    title="告警详情"
    width="960px"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    style="margin-top: 25vh;"
  >
    <page-title title="基础信息">
      <el-descriptions :column="1" label-width="80px">
        <el-descriptions-item label="告警内容" class="desc-item">{{ detail.alarmContext }}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="2" label-width="80px">
        <el-descriptions-item label="告警等级" class="desc-item">
          <el-tag :type="getAlarmLevelType(detail.alarmLevel)">{{ detail.alarmLevelText }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="告警时间" class="desc-item">{{ detail.alarmTime }}</el-descriptions-item>
        <el-descriptions-item label="设备名称" class="desc-item">{{ detail.deviceName }}</el-descriptions-item>
        <el-descriptions-item label="属地单位" class="desc-item">{{ detail.localUnitText }}</el-descriptions-item>
        <el-descriptions-item label="告警位置" class="desc-item">{{ detail.houseAddress }}</el-descriptions-item>
        <el-descriptions-item label="责任人" class="desc-item">{{ detail.responsiblePerson }}</el-descriptions-item>
        <el-descriptions-item label="责任人电话" class="desc-item">{{ detail.responsiblePhone }}</el-descriptions-item>

        <el-descriptions-item label="处理状态" class="desc-item">
          <el-tag :type="detail.handleStatus === true ? 'success' : 'warning'">
            {{ detail.handleStatus === true ? '已处理' : '未处理' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="1" label-width="80px">
        <el-descriptions-item v-if="detail.handleStatus === true" label="处理说明" class="desc-item">{{ detail.handleMemo||暂无 }}</el-descriptions-item>
      </el-descriptions>
    </page-title>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="detail.status === '0'"
          type="primary"
          style="background-color: #165DFF; border: none"
          @click="handleProcess"
        >处理</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import PageTitle from '@/components/PageTitle/index.vue'
import { getAlarmDetail } from '@/api/alarm'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  logId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'process'])

const dialogVisible = ref(props.visible)
const detail = reactive({

})

const getAlarmLevelType = (level) => {
  const typeMap = {
    '一级': 'one',
    '二级': 'two',
    '三级': 'three',
    '四级': 'four'
  }
  return typeMap[level] || 'info'
}

// 获取告警详情
const getDetail = async() => {
  try {
    const res = await getAlarmDetail(props.logId)
    if (res.code === 200) {
      Object.assign(detail, res.data)
    } else {
      ElMessage.error(res.message || '获取详情失败')
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取详情失败')
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 处理告警
const handleProcess = () => {
  emit('update:visible', false)
  // 通知父组件打开处理弹窗
  emit('process')
}

// 监听visible变化
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
    if (val && props.logId) {
      getDetail()
    }
  }
)

// 监听dialogVisible变化
watch(
  () => dialogVisible.value,
  (val) => {
    emit('update:visible', val)
  }
)
</script>

<style scoped>
:deep(.el-descriptions) {
  padding: 0;
}

:deep(.el-descriptions__header) {
  display: none;
}

:deep(.desc-item) {
  padding: 12px 0;
  margin-right: 32px;
}

:deep(.el-descriptions__label) {
  width: 100px;
  color: #86909C;
  text-align: right;
}

:deep(.el-descriptions__content) {
  color: #1D2129;
}

.dialog-footer {
  padding: 20px 0;
  text-align: right;
}
</style>
