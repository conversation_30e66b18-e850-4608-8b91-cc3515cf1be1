<template>
  <el-dialog
    v-model="dialogVisible"
    title="处理告警"
    width="960px"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    style="margin-top: 25vh;"
  >
    <page-title title="基础信息">
      <el-descriptions :column="1">
        <el-descriptions-item label="告警内容" class="desc-item">{{ alarmInfo.alarmContext }}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="2">
        <el-descriptions-item label="告警等级" class="desc-item">
          <el-tag :type="getAlarmLevelType(alarmInfo.alarmLevel)">{{ alarmInfo.alarmLevelText }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="告警时间" class="desc-item">{{ alarmInfo.alarmTime }}</el-descriptions-item>
        <el-descriptions-item label="设备名称" class="desc-item">{{ alarmInfo.deviceName }}</el-descriptions-item>
        <el-descriptions-item label="属地单位" class="desc-item">{{ alarmInfo.localUnitText }}</el-descriptions-item>
        <el-descriptions-item label="告警位置" class="desc-item">{{ alarmInfo.houseAddress }}</el-descriptions-item>
        <el-descriptions-item label="责任人" class="desc-item">{{ alarmInfo.responsiblePerson }}</el-descriptions-item>
        <el-descriptions-item label="责任人电话" class="desc-item">{{ alarmInfo.responsiblePhone }}</el-descriptions-item>
        <el-descriptions-item label="处理状态" class="desc-item">
          <el-tag :type="alarmInfo.handleStatus === true ? 'success' : 'warning'">
            {{ alarmInfo.handleStatus === true ? '已处理' : '未处理' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </page-title>

    <page-title title="处理信息">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="处理说明" prop="handleMemo">
          <el-input
            v-model="form.handleMemo"
            type="textarea"
            :rows="4"
            placeholder="请输入处理说明"
          />
        </el-form-item>
      </el-form>
    </page-title>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" style="background-color: #165DFF; border: none" @click="handleSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { processAlarm, getAlarmDetail } from '@/api/alarm'
import { ElMessage } from 'element-plus'
import PageTitle from '@/components/PageTitle/index.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  logId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'success'])

const dialogVisible = ref(props.visible)
const formRef = ref(null)
const alarmInfo = ref({})

const form = reactive({
  processRemark: ''
})

const rules = {
  processRemark: [
    { required: true, message: '请输入处理说明', trigger: 'blur' }
  ]
}

const getAlarmLevelType = (level) => {
  const typeMap = {
    '一级': 'one',
    '二级': 'two',
    '三级': 'three',
    '四级': 'four'
  }
  return typeMap[level] || 'info'
}

// 获取告警详情
const getDetail = async() => {
  try {
    const res = await getAlarmDetail(props.logId)
    if (res.code === 200) {
      alarmInfo.value = res.data
    }
  } catch (error) {
    console.error(error)
  }
}

// 关闭弹窗
const handleClose = () => {
  formRef.value?.resetFields()
  alarmInfo.value = {}
  emit('update:visible', false)
}

// 提交处理
const handleSubmit = async() => {
  if (!formRef.value) return

  await formRef.value.validate()
  try {
    const res = await processAlarm({
      logId: props.logId,
      ...form
    })
    if (res.code === 200) {
      ElMessage.success('处理成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res.message || '处理失败')
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('处理失败')
  }
}

// 监听visible变化
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
    if (val && props.logId) {
      getDetail()
    }
  }
)

// 监听dialogVisible变化
watch(
  () => dialogVisible.value,
  (val) => {
    emit('update:visible', val)
  }
)
</script>

<style scoped>
:deep(.el-descriptions) {
  padding: 0;
}

:deep(.el-descriptions__header) {
  display: none;
}

:deep(.desc-item) {
  padding: 12px 0;
  margin-right: 32px;
}

:deep(.desc-item .el-descriptions__label) {
  width: 100px;
  color: #86909C;
}

:deep(.desc-item .el-descriptions__content) {
  color: #1D2129;
}

.dialog-footer {
  padding: 20px 0;
  text-align: right;
}
</style>
