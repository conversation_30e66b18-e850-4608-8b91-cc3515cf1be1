<template>
  <div id="screen" class="data-screen">
    <div class="screen-bg">
      <!-- 顶部信息栏 -->
      <div class="top-bar">
        <div class="left-info">
          <div class="location-group">
            <img class="location-icon" src="@/assets/screen/address2.png" alt="位置">
            <span class="location-text">重庆</span>
          </div>
          <div class="weather-group">
            <img class="weather-icon" src="@/assets/screen/wether.svg" alt="天气">
            <span class="weather-text">多云</span>
          </div>
          <span class="temperature">17℃~24℃</span>
        </div>
        <div class="center-title">
          <div class="title-text">默宁监测数据中心</div>
        </div>
        <div class="right-info">
          <div class="datetime-info">
            <span class="date">{{ currentDate }}</span>
            <span class="week">{{ currentWeek }}</span>
            <span class="time">{{ currentTime }}</span>
          </div>
          <div class="workspace-group" @click="goToManage">
            <span class="workspace-text">工作台</span>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <div class="data-cards-container">
          <DataCard
            title="设备总数"
            :number="cockpitDeviceCount.total||0"
            :icon-src="totalIcon"
          />
          <DataCard
            title="在线设备数"
            :number="cockpitDeviceCount.online||0"
            :icon-src="onlineIcon"
          />
          <DataCard
            title="正常设备数"
            :number="cockpitDeviceCount.normal||0"
            :icon-src="normalIcon"
          />
          <DataCard
            title="异常设备数"
            :number="cockpitDeviceCount.abnormal||0"
            :icon-src="abnormalIcon"
          />
        </div>

        <div class="content-blocks-container">
          <div class="left-blocks">
            <ContentBlock
              title="设备类型"
              title-type="short"
              :width="616"
              :height="215"
            >
              <div class="device-type-content" style="padding:16px">
                <DeviceType :device-types="cockpitDeviceType" />
              </div>
            </ContentBlock>

            <ContentBlock
              title="设备异常提示"
              title-type="short"
              :width="616"
              :height="520"
            >
              <div class="device-alert-content">
                <!-- 设备异常提示内容 - 移除deviceList属性，组件内部自行获取数据 -->
                <DeviceAlert />
              </div>
            </ContentBlock>
          </div>

          <div class="right-block">
            <ContentBlock
              title="设备信息"
              title-type="long"
              :width="1244"
              :height="755"
            >
              <div class="device-info-content">
                <DeviceInfo />
              </div>
            </ContentBlock>
          </div>
        </div>
      </div>

      <!-- 底部背景 -->
      <div class="footer-bg" />
    </div>
  </div>
</template>

<script setup>
import autofit from 'autofit.js'
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import DataCard from '@/components/DataCard.vue'
import ContentBlock from '@/components/ContentBlock.vue'
import DeviceType from '@/components/DeviceType.vue'
import DeviceAlert from '@/components/DeviceAlert.vue'
import DeviceInfo from '@/components/DeviceInfo.vue'
import {
  getCockpitDeviceCount,
  getCockpitDeviceList,
  getCockpitDeviceType
} from '@/api/cockpit'

// 导入图标
import totalIcon from '@/assets/screen/total-icon.svg'
import onlineIcon from '@/assets/screen/online-icon.svg'
import normalIcon from '@/assets/screen/normal-icon.svg'
import abnormalIcon from '@/assets/screen/abnormal-icon.svg'

const currentTime = ref('')
const currentDate = ref('')
const currentWeek = ref('')
const router = useRouter()

// cockpit接口相关变量
const cockpitDeviceCount = ref({
  'total': 0,
  'online': 0,
  'normal': 0,
  'abnormal': 0
})
const cockpitDeviceList = ref([])
const cockpitDeviceType = ref([])

const updateDateTime = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const weeks = ['日', '一', '二', '三', '四', '五', '六']

  currentDate.value = `${year}.${month}.${day}`
  currentTime.value = `${hours}:${minutes}:${seconds}`
  currentWeek.value = `星期${weeks[now.getDay()]}`
}

let timer

const fetchCockpitData = async() => {
  try {
    const [countRes, listRes, typeRes] = await Promise.all([
      getCockpitDeviceCount(),
      getCockpitDeviceList(),
      getCockpitDeviceType()
    ])
    if (countRes.code === 200) cockpitDeviceCount.value = countRes.data
    if (listRes.code === 200) cockpitDeviceList.value = listRes.data || listRes.rows || []
    if (typeRes.code === 200) cockpitDeviceType.value = typeRes.data || typeRes.rows || []
  } catch (e) {
    // 可加错误提示
  }
}
updateDateTime()
fetchCockpitData()

onMounted(() => {
  autofit.init({
    dw: 1920,
    dh: 1080,
    el: '#screen',
    resize: true,
    limit: 0.01
  })

  timer = setInterval(updateDateTime, 1000)

  // 简化：直接添加一个类来标识当前是数据大屏
  document.body.classList.add('is-datascreen-page')

  // 添加样式，仅当body有特定类时才应用样式
  const styleEl = document.createElement('style')
  styleEl.innerHTML = `
    /* 只在数据大屏页面应用这些样式 */
    body.is-datascreen-page .el-select-dropdown,
    body.is-datascreen-page .el-popper.el-select__popper,
    body.is-datascreen-page .el-popper.is-light {
      background-color: #0e3c8c !important;
      --el-bg-color: #0e3c8c !important;
      --el-bg-color-overlay: #0e3c8c !important;
      --el-fill-color-blank: #0e3c8c !important;
      border: 1px solid #1E54B7 !important;
    }

    body.is-datascreen-page .el-select-dropdown__list {
      background-color: #0e3c8c !important;
    }

    body.is-datascreen-page .el-select-dropdown__item {
      background-color: #0e3c8c !important;
      color: rgba(255, 255, 255, 0.8) !important;
    }

    body.is-datascreen-page .el-select-dropdown__item.hover,
    body.is-datascreen-page .el-select-dropdown__item:hover,
    body.is-datascreen-page .el-select-dropdown__item.selected {
      background-color: #1050BC !important;
      color: #FFFFFF !important;
    }

    body.is-datascreen-page .el-tree {
      background-color: #0e3c8c !important;
      color: rgba(255, 255, 255, 0.8) !important;
    }

    body.is-datascreen-page .el-tree-node__content {
      background-color: #0e3c8c !important;
    }

    body.is-datascreen-page .el-tree-node.is-current > .el-tree-node__content,
    body.is-datascreen-page .el-tree-node__content:hover {
      background-color: #1050BC !important;
      color: #FFFFFF !important;
    }

    body.is-datascreen-page .el-popper__arrow::before {
      background-color: #0e3c8c !important;
      border-color: #1E54B7 !important;
    }
  `
  document.head.appendChild(styleEl)
})

onBeforeUnmount(() => {
  autofit && autofit.off()
  clearInterval(timer)

  // 移除标识类，确保离开页面时不影响其他页面
  document.body.classList.remove('is-datascreen-page')
})

const goToManage = () => {
  // 直接跳转到设备管理页面
  router.push('/index')
}
</script>

<style scoped>
@import '@/assets/styles/fonts.scss';

/* 数据大屏下拉菜单全局样式 - 只保留CSS变量 */
:root {
  --el-select-dropdown-bg-color: #0e3c8c;
  --el-select-dropdown-hover-bg-color: #1050BC;
}

.data-screen {
  width: 1920px;
  height: 1080px;
  position: relative;
  overflow: hidden;
}

.screen-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(@/assets/screen/screen-bg.png) lightgray 50% / cover no-repeat;
  mix-blend-mode: plus-lighter;
  display: flex;
  flex-direction: column;
}

.top-bar {
  width: 100%;
  height: 78px;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: url(@/assets/screen/head-title.png) no-repeat center top;
  background-size: 100% 100%;
}

.left-info {
  display: flex;
  align-items: center;
}

.location-group {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-right: 26px;
}

.weather-group {
  display: flex;
  align-items: center;
  margin-right: 26px;
}

.weather-group .weather-icon {
  margin-right: 4px;
}

.weather-group .weather-text {
  margin-right: 8px;
}

.datetime-info {
  display: flex;
  align-items: center;
  margin-right: 26px;
}

.datetime-info .date {
  margin-right: 12px;
}

.datetime-info .week {
  margin-right: 12px;
}

.center-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-family: "Alimama ShuHeiTi";
  font-size: 48px;
  font-weight: 700;
  line-height: normal;
  letter-spacing: 4.7px;
  background: linear-gradient(180deg, rgba(244, 251, 255, 1) 0%, rgba(60, 193, 255, 1) 100%);
  -webkit-background-clip: text;
  color: #FFFFFF;
  text-shadow: 3px 2px 0px rgba(24, 18, 125, 0.25), 0px 0px 8px #0080FF;
  text-align: center;
}

.right-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.workspace-group {
  margin-top:8px;
  background: url('@/assets/screen/workbench.png') no-repeat center center;
  background-size: 80px 32px;
  width: 80px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.workspace-text {
  font-family: 'PingFang SC';
  font-size: 16px;
  font-weight: 500;
  color: #FFFFFF;
  text-shadow: 0px 0px 4px rgba(0, 128, 255, 0.5);
}

.location-icon {
  width: 36px;
  height: 36px;
  object-fit: contain;
}

.weather-icon, .workspace-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.location-text {
  font-family: 'PingFang SC';
  font-size: 22px;
  font-weight: 600;
  color: #D8F0FF;
  margin-left:-10px;
}

.weather-text, .temperature {
  font-family: 'PingFang SC';
  font-size: 16px;
  font-weight: 500;
  color: rgba(216, 240, 255, 0.8);
}

.date, .week, .time {
  font-family: 'PingFang SC';
  font-size: 16px;
  font-weight: 500;
  color: rgba(216, 240, 255, 0.8);
}

.main-content {
  flex: 1;
  position: relative;
  z-index: 1;
  padding-top: 16px;
}

.footer-bg {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 58px;
  background: url(@/assets/screen/footer.png) no-repeat center bottom;
  background-size: 100% 100%;
  z-index: 0;
}

.data-cards-container {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  padding: 0px 20px;
  justify-content:center;
}

.content-blocks-container {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding: 20px;
}

.left-blocks {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex-shrink: 0;
}

.right-block {
  flex-shrink: 0;
}

.device-type-content,
.device-info-content {
  height: 100%;
}

.device-alert-content {
  height: 100%;
  max-height: calc(520px - 64px); /* 520px是ContentBlock的高度，减去标题和padding */
  box-sizing: border-box;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
