<template>
  <div class="content-block" :style="blockStyle">
    <div class="block-header" :class="titleType">
      <span class="title-text">{{ title }}</span>
    </div>
    <div class="block-content">
      <slot />
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  titleType: {
    type: String,
    default: 'short',
    validator: (value) => ['short', 'long'].includes(value)
  },
  width: {
    type: [String, Number],
    default: '100%'
  },
  height: {
    type: [String, Number],
    default: '100%'
  }
})

const blockStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height
}))
</script>

<style scoped>
.content-block {
  background: linear-gradient(180deg, rgba(35, 104, 240, 0.1) 0%, rgba(35, 104, 240, 0.01) 100%);
  padding: 16px 16px 8px 16px;
  border: 1px solid rgba(35, 104, 240, 0.2);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.block-header {
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  position: relative;
  flex-shrink: 0;
}

.block-header.short {
  background: url(@/assets/screen/title-short.png) no-repeat;
  background-size: 100% 100%;
}

.block-header.long {
  background: url(@/assets/screen/title-long.png) no-repeat;
  background-size: 100% 100%;
}

.title-text {
  font-family: 'Alibaba PuHuiTi 3.0';
  font-size: 20px;
  font-weight: 800;
  background: linear-gradient(180deg,
    rgba(255, 244, 230, 1) 0%,
    rgba(255, 239, 222, 1) 25%,
    rgba(255, 200, 123, 1) 50%,
    rgba(255, 119, 52, 1) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  padding-left: 16px;
  padding-bottom: 4px;
  letter-spacing: 0;
}

.block-content {
  flex: 1;
  overflow: auto;
  /* 添加滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(35, 104, 240, 0.3) transparent;
}

/* 自定义滚动条样式 */
.block-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.block-content::-webkit-scrollbar-track {
  background: transparent;
}

.block-content::-webkit-scrollbar-thumb {
  background-color: rgba(35, 104, 240, 0.3);
  border-radius: 3px;
}

.block-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(35, 104, 240, 0.5);
}
</style>
