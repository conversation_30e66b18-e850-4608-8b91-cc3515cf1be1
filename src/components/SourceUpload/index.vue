<template>
  <el-upload
    ref="uploadRef"
    v-model:file-list="fileList"
    class="source-upload"
    :action="uploadFileUrl"
    :limit="limit"
    drag
    :headers="headers"
    multiple
    :disabled="disabled"
    show-file-list
    :before-upload="beforeUpload"
    :on-success="onSuccess"
    :on-error="onError"
    :on-exceed="handleExceed"
  >
    <div v-if="(limit === 0 || fileList?.length < limit) && !disabled" class="upload-empty">
      <el-icon>
        <Upload />
      </el-icon>
      <div class="empty-text">
        点击或拖拽文件到此处上传
      </div>
    </div>
    <template #tip>
      <div v-if="!disabled" class="upload-tips">
        请上传<template v-if="fileSize">
          大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
        </template><template v-if="fileType">
          格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>的
        </template>文件</div>
    </template>
    <template #file="{ file }">
      <div class="source-show" :class="file.status === 'uploading' ? 'uploading' : ''">
        <div class="show-left">
          <img :src="getFileIcon(file.url || file.name)" alt="" class="show-icon">
          <a :href="file.url" target="_blank" class="show-name">{{ file.name }}</a>
        </div>
        <div class="show-right">
          <el-icon v-if="file.status === 'uploading'" class="show-loading">
            <loading />
          </el-icon>
          <el-icon v-if="!disabled" class="show-del" @click="handleDel(file)">
            <delete />
          </el-icon>
        </div>
      </div>
    </template>
  </el-upload>
</template>

<script setup name="SourceUpload">
import { getToken } from '@/utils/auth'
import { requireImg } from '@/utils'
import { formContextKey, formItemContextKey } from 'element-plus'

// 获取el-form-item注入的实例
const formContext = inject(formContextKey, undefined)
const formItemContext = inject(formItemContextKey, undefined)
const { proxy } = getCurrentInstance()
const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + '/common/upload')
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  // 图片数量限制（为0时不限制）
  limit: {
    type: Number,
    default: 0
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 50
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ['png', 'jpg', 'jpeg']
  },
  disabled: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:modelValue'])

const uploadRef = ref()
const headers = ref({
  Authorization: 'Bearer ' + getToken()
})
const fileList = computed({
  get() {
    return props.modelValue ?? []
  },
  set(val) {
    emit('update:modelValue', val)
    validateEl()
  }
})
// 上传成功
const onSuccess = (res, file) => {
  console.log('onsuccess', res, file)
  const index = fileList.value.findIndex(item => item.uid === file.uid)
  fileList.value[index] = {
    name: file.name,
    uid: file.uid,
    status: 'success',
    url: res.url,
    uploadName: res.uploadName,
    size: res.size
  }
}
// 上传失败
function onError() {
  proxy.$modal.msgError('文件上传失败')
}
// 上传文件前
const beforeUpload = (file) => {
  if (props.fileType.length) {
    const fileExtension = getFileExtension(file.name)
    const isTypeOk = props.fileType.indexOf(fileExtension) >= 0
    if (!isTypeOk) {
      proxy.$modal.msgError(`文件格式不正确, 请上传${props.fileType.join('/')}格式文件!`)
      return false
    }
  }
  return true
}
// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`)
}
// 删除文件
const handleDel = (file) => {
  fileList.value = fileList.value.filter(item => item.uid !== file.uid)
}
// 根据文件名获取后缀名
function getFileExtension(str) {
  if (str.lastIndexOf('.') > -1) {
    return str.slice(str.lastIndexOf('.') + 1)
  }
}
// 根据文件后缀名获取文件图标(参数有可能是url也可能是文件名)
function getFileIcon(str) {
  // if (str.indexOf('http') === 0) {
  //   return str
  // }
  const fileExtension = getFileExtension(str)
  return requireImg('icons/svg/doc/' + fileExtension.toLowerCase() + '.svg')
}
// 使用elForm进行校验
function validateEl() {
  if (formContext && formItemContext) {
    formContext?.validateField([formItemContext.prop])
  }
}
</script>

<style lang="scss" scoped>
@keyframes rotate {
  0%{
    transform: rotate(0deg);
  }
  100%{
    transform: rotate(360deg);
  }
}
.source-upload{
  width: 100%;
  ::v-deep(.el-upload){
    width: 100%;
    .el-upload-dragger{
      padding: 0;
      border: none;
    }
  }
  .upload-empty{
    position: relative;
    width: 100%;
    height: 48px;
    border: 1px dashed #E5E6EB;
    display: flex;
    align-items: center;
    padding: 0 16px;
    color: #4E5969;
    font-size: 14px;
    line-height: 22px;
    .empty-text{
      margin-left: 8px;
    }
  }
  .upload-tips{
    color: #606266;
    font-size: 12px;
    margin-bottom: -8px;
  }
  .source-show{
    width: 100%;
    min-height: 46px;
    padding: 4px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #F7F8FA;
    border-radius: 4px;
    .show-left{
      display: flex;
      width: 100%;
      min-height: 46px;
      align-items: center;
      .show-icon{
        width: 32px;
        height: 32px;
      }
      .show-name{
        margin-left: 12px;
        text-wrap:wrap;
      }
    }
    .show-right{
      display: flex;
      align-items: center;
      .show-loading{
        animation: rotate 2s linear infinite;
      }
      .show-del{
        margin-left: 12px;
        cursor: pointer;
      }
    }
  }
}
</style>
