<template>
  <div class="custom-search">
    <el-form
      ref="queryRef"
      :model="queryParams"
      inline
      :label-width="labelWidth"
      @submit.prevent="onQuery"
    >
      <el-row :gutter="25">
        <el-col v-for="(item, index) in searchData" :key="index" :span="6">
          <el-form-item
            :label="item.label"
            :prop="item.prop"
            style="width: 100%;"
          >
            <!-- 输入框 -->
            <el-input
              v-if="item.type === 'input'"
              v-model="queryParams[item.prop]"
              :clearable="item.clearable || true"
              :placeholder="item.placeholder || '请输入'"
            />
            <!-- 下拉框 -->
            <el-select
              v-if="item.type === 'select'"
              v-model="queryParams[item.prop]"
              :clearable="item.clearable || true"
              :placeholder="item.placeholder || '请选择'"
            >
              <el-option
                v-for="opt in item.options || optionsObj[item.prop]"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              />
            </el-select>
            <!-- 日期选择 -->
            <el-date-picker
              v-if="item.type === 'date'"
              v-model="queryParams[item.prop]"
              :type="item.range ? 'daterange' : 'date'"
              :clearable="item.clearable || true"
              :value-format="item.format || 'YYYY-MM-DD'"
              :placeholder="item.placeholder || '请选择'"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
            <!-- 日期时间选择 -->
            <el-date-picker
              v-if="item.type === 'datetime'"
              v-model="queryParams[item.prop]"
              :type="item.range ? 'datetimerange' : 'datetime'"
              :clearable="item.clearable || true"
              :value-format="item.format || 'YYYY-MM-DD HH:mm:ss'"
              :placeholder="item.placeholder || '请选择'"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6" style="margin-left: auto;">
          <div style="display: flex; justify-content: flex-end;">
            <el-button
              type="primary"
              icon="Search"
              native-type="submit"
            >
              搜索
            </el-button>
            <el-button
              icon="Refresh"
              @click="onReset"
            >
              重置
            </el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup name="CustomSearch">
import { useVModel } from '@vueuse/core'
import { onMounted } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  searchData: {
    type: Array,
    required: true
  },
  onSearch: {
    type: Function,
    required: true
  },
  optionsObj: {
    type: Object,
    default: () => ({})
  },
  labelWidth: {
    type: [Number, String],
    default: 100
  }
})
const emit = defineEmits(['update:modelValue'])
const queryParams = useVModel(props, 'modelValue', emit)

// 将搜索条件转成对象形式
function getSearchParams(data) {
  const searchParams = {}
  data.forEach((item) => {
    searchParams[item.prop] = item.defaultValue
  })
  return searchParams
}
onMounted(() => {
  queryParams.value = Object.assign({}, queryParams.value, getSearchParams(props.searchData))
  onQuery()
})
// 搜索事件
function onQuery() {
  queryParams.value.pageNum = 1
  props.onSearch && props.onSearch()
}
// 重置事件
function onReset() {
  queryParams.value = Object.assign({}, queryParams.value, getSearchParams(props.searchData))
  onQuery()
}
</script>
