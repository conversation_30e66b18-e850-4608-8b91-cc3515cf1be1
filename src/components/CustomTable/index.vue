<template>
  <div class="custom-table">
    <el-table
      v-loading="loading"
      v-bind="tableProps"
      v-on="tableEvents"
    >
      <el-table-column
        v-for="col in columns"
        :key="col.prop"
        v-bind="col"
      >
        <template v-if="col.slotName" #default="scope">
          <slot
            :name="col.slotName"
            :row="scope.row"
          />
        </template>
      </el-table-column>
    </el-table>
    <div class="table-pagination">
      <el-pagination
        v-bind="{ ...defaultPaginationProps, ...paginationProps}"
        v-on="paginationEvents"
      />
    </div>
  </div>
</template>

<script setup name="CustomTable">
defineProps({
  tableProps: {
    type: Object,
    default: () => ({
      data: []
    })
  },
  tableEvents: {
    type: Object,
    default: () => ({})
  },
  columns: {
    type: Array,
    default: () => []
  },
  paginationProps: {
    type: Object,
    default: () => ({})
  },
  paginationEvents: {
    type: Object,
    default: () => ({})
  }
})
// 默认分页配置
const defaultPaginationProps = {
  background: true,
  layout: 'total, sizes, prev, pager, next',
  total: 0,
  pageSizes: [10, 20, 30, 40, 50],
  pageSize: 10,
  currentPage: 1
}

const loading = ref(false)
</script>

<style lang="scss" scoped>
.table-pagination{
  display: flex;
  justify-content: flex-end;
  margin-right: 25px;
  margin-top: 25px;
}
</style>
