<template>
  <div class="device-info device-info-select-scope">
    <!-- 搜索区域 -->
    <div class="search-container">
      <div class="search-item">
        <span class="label">设备名称</span>
        <el-input v-model="searchForm.deviceName" placeholder="请输入" class="search-input" />
      </div>
      <div class="search-item">
        <span class="label">组织筛选</span>
        <el-tree-select
          v-model="searchForm.localUnit"
          :data="unitOptions"
          :props="{ label: 'label', value: 'id', children: 'children' }"
          value-key="id"
          placeholder="请选择"
          check-strictly
          class="search-select"
        />
      </div>
      <div class="search-item">
        <span class="label">在线状态</span>
        <el-select
          v-model="searchForm.onlineStatus"

          placeholder="全部"
          class="search-select"
        >
          <el-option label="全部" value="" />
          <el-option label="在线" :value="true" />
          <el-option label="离线" :value="false" />
        </el-select>
      </div>
      <div class="search-item">
        <span class="label">告警状态</span>
        <el-select v-model="searchForm.faultStatus" placeholder="全部" class="search-select">
          <el-option label="全部" value="" />
          <el-option label="告警" value="1" />
          <el-option label="未告警" value="0" />
        </el-select>
      </div>
      <!-- <div class="search-item">
        <span class="label">运行状态</span>
        <el-select v-model="searchForm.status" placeholder="请选择" class="search-select">
          <el-option label="全部" value="" />
          <el-option label="运行" value="0" />
          <el-option label="未运行" value="1" />
        </el-select>
      </div> -->
      <div class="search-buttons">
        <el-button class="reset-btn" icon="refresh" @click="handleReset">重置</el-button>
        <el-button
          type="primary"
          class="search-btn"
          icon="search"
          @click="handleSearch"
        >查询</el-button>

      </div>
    </div>

    <!-- 设备列表展示 -->
    <div class="device-list-container">
      <div class="device-list">
        <div
          v-for="(device) in deviceList"
          :key="device.deviceId"
          class="device-card"
          @click="handleDeviceClick(device.deviceId)"
        >
          <div class="device-card-header">
            <span class="device-name">{{ device.deviceName }}</span>
            <span :class="['device-status', device.onlineStatus === true ? 'online' : 'offline']">
              {{ device.onlineStatus === true ? '在线' : '离线' }}
            </span>
          </div>
          <div class="device-card-content">
            <div class="device-info-item">
              <span class="info-label">设备SN:</span>
              <span class="info-value">{{ device.imei }}</span>
            </div>
            <div class="device-info-item">
              <span class="info-label">在线状态:</span>
              <span class="info-value">{{ device.onlineStatus === true ? '在线' : '离线' }}</span>
            </div>
            <div class="device-info-item">
              <span class="info-label">属地单位:</span>
              <span class="info-value">{{ device.localUnitText }}</span>
            </div>
            <div class="device-info-item">
              <span class="info-label">安装地址:</span>
              <span class="info-value">{{ device.houseAddress }}</span>
            </div>
            <div class="device-info-item">
              <span class="info-label">更新时间:</span>
              <span class="info-value">{{ device.updateTime||device.createTime }}</span>
            </div>
          </div>

        </div>
      </div>
    </div>

    <!-- 分页信息 -->
    <div class="pagination-container">
      <div class="pagination-info">
        <span>共{{ total }}条</span>
      </div>
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 40]"
        layout="prev, pager, next, jumper, sizes"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <HistoryReportDialog
      v-if="showDialog"
      :visible="showDialog"
      :device-id="dialogDeviceId"
      @close="showDialog = false"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { getCockpitDeviceList } from '@/api/cockpit'
import { deptTreeSelect } from '@/api/system/user'
import HistoryReportDialog from './HistoryReportDialog.vue'

// 搜索表单
const searchForm = reactive({
  deviceName: '',
  localUnit: '',
  onlineStatus: '',
  faultStatus: '',
  status: ''
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 当前选中的设备
const activeDevice = ref(null)
const dialogDeviceId = ref(null)
const showDialog = ref(false)

// 设备数据
const deviceList = ref([])

// 组织单位数据
const unitOptions = ref([])

// 获取组织单位数据
const fetchUnitOptions = async() => {
  try {
    const res = await deptTreeSelect()
    if (res.code === 200) {
      unitOptions.value = res.data
    }
  } catch (e) {
    console.error('获取组织单位数据失败', e)
  }
}

// 获取设备数据（接口）
const fetchDeviceList = async() => {
  const params = {
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    deviceName: searchForm.deviceName,
    localUnit: searchForm.localUnit,
    onlineStatus: searchForm.onlineStatus,
    faultStatus: searchForm.faultStatus,
    status: searchForm.status
  }
  try {
    const res = await getCockpitDeviceList(params)
    // 假设返回结构为 { rows: [], total: 0 }，如有不同请调整
    deviceList.value = res.rows || []
    total.value = res.total || 0
    // 默认选中第一个设备
    if (deviceList.value.length > 0) {
      activeDevice.value = deviceList.value[0].id
    } else {
      activeDevice.value = null
    }
  } catch (e) {
    deviceList.value = []
    total.value = 0
    activeDevice.value = null
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchDeviceList()
}

// 重置搜索条件
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  currentPage.value = 1
  fetchDeviceList()
}

// 处理设备点击
const handleDeviceClick = (id) => {
  activeDevice.value = id
  dialogDeviceId.value = id
  showDialog.value = true
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchDeviceList()
}

// 处理每页条数变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  fetchDeviceList()
}

// 添加动态样式
const addDropdownStyles = () => {
  const styleElement = document.createElement('style')
  styleElement.textContent = `
    /* 限定选择器范围，只影响数据大屏和当前组件 */
    .data-screen .el-select-dropdown__list,
    .device-info .el-select-dropdown__list,
    .device-info-select-scope .el-select-dropdown__list {
      background-color: #0e3c8c !important;
    }

    .data-screen .el-select-dropdown,
    .device-info .el-select-dropdown,
    .device-info-select-scope .el-select-dropdown,
    .data-screen .el-popper.el-select__popper,
    .device-info .el-popper.el-select__popper,
    .device-info-select-scope .el-popper.el-select__popper,
    .data-screen .el-popper.is-light,
    .device-info .el-popper.is-light,
    .device-info-select-scope .el-popper.is-light {
      background-color: #0e3c8c !important;
      --el-bg-color: #0e3c8c !important;
      --el-bg-color-overlay: #0e3c8c !important;
      --el-fill-color-blank: #0e3c8c !important;
      border-color: #1E54B7 !important;
    }

    .data-screen .el-select-dropdown__item,
    .device-info .el-select-dropdown__item,
    .device-info-select-scope .el-select-dropdown__item {
      background-color: #0e3c8c !important;
      color: rgba(255, 255, 255, 0.8) !important;
    }

    .data-screen .el-select-dropdown__item.hover,
    .data-screen .el-select-dropdown__item:hover,
    .data-screen .el-select-dropdown__item.selected,
    .device-info .el-select-dropdown__item.hover,
    .device-info .el-select-dropdown__item:hover,
    .device-info .el-select-dropdown__item.selected,
    .device-info-select-scope .el-select-dropdown__item.hover,
    .device-info-select-scope .el-select-dropdown__item:hover,
    .device-info-select-scope .el-select-dropdown__item.selected {
      background-color: #1050BC !important;
      color: #FFFFFF !important;
    }

    .data-screen .el-tree,
    .device-info .el-tree,
    .device-info-select-scope .el-tree {
      background-color: #0e3c8c !important;
      color: rgba(255, 255, 255, 0.8) !important;
    }

    .data-screen .el-tree-node__content,
    .device-info .el-tree-node__content,
    .device-info-select-scope .el-tree-node__content {
      background-color: #0e3c8c !important;
    }

    .data-screen .el-tree-node.is-current > .el-tree-node__content,
    .data-screen .el-tree-node__content:hover,
    .device-info .el-tree-node.is-current > .el-tree-node__content,
    .device-info .el-tree-node__content:hover,
    .device-info-select-scope .el-tree-node.is-current > .el-tree-node__content,
    .device-info-select-scope .el-tree-node__content:hover {
      background-color: #1050BC !important;
      color: #FFFFFF !important;
    }
  `
  document.head.appendChild(styleElement)
}

onMounted(() => {
  fetchUnitOptions()
  fetchDeviceList()
  addDropdownStyles() // 添加动态样式
})

</script>

<style scoped>
.device-info {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  color: rgba(255, 255, 255, 0.8);
}

/* 以下样式只在当前组件中生效 */
:deep(.device-info .el-select-dropdown__list),
:deep(.device-info-select-scope .el-select-dropdown__list) {
  background-color: #0e3c8c !important;
}

:deep(.device-info .el-select-dropdown),
:deep(.device-info-select-scope .el-select-dropdown) {
  background-color: #0e3c8c !important;
}

:deep(.device-info .el-tree),
:deep(.device-info-select-scope .el-tree) {
  background-color: #0e3c8c !important;
  color: rgba(255, 255, 255, 0.8) !important;
}

:deep(.device-info .el-tree-node__content),
:deep(.device-info-select-scope .el-tree-node__content) {
  background-color: #0e3c8c !important;
}

:deep(.device-info .el-select-dropdown__item),
:deep(.device-info-select-scope .el-select-dropdown__item) {
  background-color: #0e3c8c !important;
  color: rgba(255, 255, 255, 0.8) !important;
}

:deep(.device-info .el-select-dropdown__item.hover),
:deep(.device-info .el-select-dropdown__item:hover),
:deep(.device-info .el-select-dropdown__item.selected),
:deep(.device-info-select-scope .el-select-dropdown__item.hover),
:deep(.device-info-select-scope .el-select-dropdown__item:hover),
:deep(.device-info-select-scope .el-select-dropdown__item.selected) {
  background-color: #1050BC !important;
  color: #FFFFFF !important;
}

:deep(.device-info .el-tree-node.is-current > .el-tree-node__content),
:deep(.device-info-select-scope .el-tree-node.is-current > .el-tree-node__content) {
  background-color: #1050BC !important;
  color: #FFFFFF !important;
}

:deep(.device-info .el-tree-node__content:hover),
:deep(.device-info-select-scope .el-tree-node__content:hover) {
  background-color: #1050BC !important;
}

/* 搜索区域样式 */
.search-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 16px;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 16px;
  background: rgba(35, 66, 127, 0.3);
  border-radius: 4px;
}

.search-item {
  display: flex;
  align-items: center;

}

.label {
  margin-right: 8px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
}

.search-input,
.search-select {
  background: rgba(35, 66, 127, 0.3);
  height: 32px;
  width: 280px;
}

:deep(.el-input__wrapper),
:deep(.el-select .el-input__wrapper),
:deep(.el-tree-select .el-input__wrapper) {
  background-color: rgba(35, 66, 127, 0.3);
  box-shadow: none;
  border: 1px solid rgba(35, 104, 240, 0.3);
}

:deep(.el-input__inner),
:deep(.el-input input) {
  color: #fff !important;
  height: 32px;
}

:deep(.el-select .el-input__inner),
:deep(.el-tree-select .el-input__inner) {
  color: rgba(255, 255, 255, 0.8);
}

.search-buttons {
  width:346px ;
  display: flex;
  justify-content: flex-end;
  gap: 8px;

}

.search-btn {
  background: #165DFF;
  border: none;
}

.reset-btn {
  background: rgba(35, 66, 127, 0.3);
  border: 1px solid rgba(35, 104, 240, 0.3);
  color: rgba(255, 255, 255, 0.8);
}

/* 设备列表样式 */
.device-list-container {
  flex: 1;
  overflow-y: auto;
}

.device-list {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 20px;
  max-width: 1200px;
}

.device-card {
  width: 224px;
  height: 146px;
  background: rgba(35, 66, 127, 0.2);
  border: 1px solid rgba(35, 104, 240, 0.3);
  border-radius: 4px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0;
}

.device-card:hover {
  background: rgba(35, 66, 127, 0.4);
  border-color: rgba(0, 147, 255, 0.5);
}

.device-card.active {
  background: url(@/assets/screen/data-selected.png) no-repeat;
  background-size: 100% 100%;
  border-color: rgba(0, 147, 255, 0.8);
}

.device-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.device-name {
  font-size: 14px;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.9);
}

.device-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 2px;
}

.device-status.online {
  background: rgba(0, 255, 0, 0.2);
  color: #00FF00;
}

.device-status.offline {
  background: rgba(255, 0, 0, 0.2);
  color: #FF6B6B;
}

.device-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
}

.device-info-item {
  display: flex;
  align-items: center;
}

.info-label {
  color: rgba(255, 255, 255, 0.6);

  width: 56px;

}

.info-value {
  color: rgba(255, 255, 255, 0.8);
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.device-card-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 4px;
}

.run-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 2px;
}

.run-status.normal {
  background: rgba(0, 255, 0, 0.2);
  color: #00FF00;
}

.run-status.abnormal {
  background: rgba(255, 0, 0, 0.2);
  color: #FF6B6B;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0px;
  max-width: 700px;
  color: #C2DFFF;
}

.pagination-info {
  font-size: 14px;
  color: #C2DFFF;
}

:deep(.el-pagination) {
  --el-pagination-bg-color: transparent;
  --el-pagination-text-color: #C2DFFF;
  --el-pagination-button-color: #C2DFFF;
  --el-pagination-button-bg-color: rgba(35, 66, 127, 0.3);
  --el-pagination-button-disabled-color: rgba(194, 223, 255, 0.5);
  --el-pagination-button-disabled-bg-color: rgba(35, 66, 127, 0.1);
  --el-pagination-hover-color: #00FFFF;
  color: #C2DFFF;
}

/* 确保分页组件中的所有文字都使用正确的颜色 */
:deep(.el-pagination span),
:deep(.el-pagination button),
:deep(.el-pagination .el-pager li),
:deep(.el-pagination .el-pagination__total),
:deep(.el-pagination .el-pagination__jump),
:deep(.el-pagination .el-input__inner),
:deep(.el-pagination .el-pagination__sizes) {
  color: #C2DFFF;
}

:deep(.el-pagination .el-select .el-input) {
  width: 100px;
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
  color: #C2DFFF;
}

:deep(.el-select__wrapper) {
  background-color: rgba(35, 66, 127, 0.3) !important;
  border: 1px solid rgba(35, 104, 240, 0.3) !important;
  box-shadow: 0 0 0 0.5px rgba(35, 104, 240, 0.3) inset;
}

/* 修改选择器的placeholder文本颜色 */
:deep(.el-select__placeholder),
:deep(.el-select-v2__placeholder),
:deep(.el-tree-select__placeholder),
:deep(.el-input__placeholder),
:deep(.el-input input::placeholder) {
  color: #fff !important;
}

/* 修改选择器中已选项的文本颜色 */
:deep(.el-select .el-select__tags-text),
:deep(.el-select-dropdown__item.selected) {
  color: #fff !important;
}
</style>
