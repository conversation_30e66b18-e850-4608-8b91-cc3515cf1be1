<template>
  <div class="page-title">
    <div class="title-text">
      <!-- <div class="vertical-line" /> -->
      <img src="@/assets/images/plus-circle.svg" class="title-icon">
      {{ title }}
    </div>
    <slot />
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  }
})
</script>

<style scoped>
.page-title {
  padding: 16px;
  background-color: #fff;
  margin-bottom: 16px;
  border-radius: 4px;
}

.title-text {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.vertical-line {
  width: 3px;
  height: 16px;
  background-color: #1890ff;
  margin-right: 8px;
}

.title-icon {
  width: 16px;
  height: 16px;
}
</style>
