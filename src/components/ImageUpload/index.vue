<template>
  <div class="component-upload-image">
    <el-upload
      ref="imageUpload"
      multiple
      :action="uploadImgUrl"
      list-type="picture-card"
      :on-success="handleUploadSuccess"
      :before-upload="handleBeforeUpload"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :before-remove="handleDelete"
      :show-file-list="true"
      :headers="headers"
      :file-list="fileList"
      :on-preview="handlePictureCardPreview"
      :class="{ hide: fileList.length >= limit }"
    >
      <el-icon class="avatar-uploader-icon">
        <plus />
      </el-icon>
      <template #file="{ file }">
        <div class="image-item">
          <img
            class="el-upload-list__item-thumbnail"
            :src="file.url"
          >
          <span class="el-upload-list__item-actions">
            <template v-if="valueType === 'array' && canSwap && modelValue && modelValue.length > 1">
              <span
                v-if="!disabled && getFileIndex(file.url) !== 0"
                class="el-upload-list__item-preview"
                @click="handleMoveLeft(file)"
              >
                <el-icon><back /></el-icon>
              </span>
              <span
                v-if="!disabled && getFileIndex(file.url) !== modelValue.length - 1"
                class="el-upload-list__item-preview"
                @click="handleMoveRight(file)"
              >
                <el-icon><right /></el-icon>
              </span>
            </template>
            <span
              class="el-upload-list__item-preview"
              @click="handlePictureCardPreview(file)"
            >
              <el-icon><zoom-in /></el-icon>
            </span>
            <span
              v-if="!disabled"
              class="el-upload-list__item-delete"
              @click="handleDelete(file)"
            >
              <el-icon><delete /></el-icon>
            </span>
          </span>
        </div>
      </template>
    </el-upload>
    <!-- 上传提示 -->
    <div
      v-if="showTip"
      class="el-upload__tip"
    >
      请上传
      <template v-if="fileSize">
        大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
      <template v-if="fileType">
        格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
      </template>
      的文件
    </div>

    <el-dialog
      v-model="dialogVisible"
      title="预览"
      width="800px"
      append-to-body
    >
      <img
        :src="dialogImageUrl"
        style="display: block; max-width: 100%; margin: 0 auto"
      >
    </el-dialog>
  </div>
</template>

<script setup>
/* eslint-disable eqeqeq */
import { getToken } from '@/utils/auth'
import { formContextKey, formItemContextKey } from 'element-plus'

const props = defineProps({
  modelValue: [String, Object, Array],
  // 传入的图片格式
  valueType: {
    type: String,
    default: 'string' // string, array
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 图片数量限制
  limit: {
    type: Number,
    default: 5
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 50
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ['png', 'jpg', 'jpeg']
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true
  },
  // 是否可以交换（主要用于banner处）
  canSwap: {
    type: Boolean,
    default: false
  }
})

// 获取el-form-item注入的实例
const formContext = inject(formContextKey, undefined)
const formItemContext = inject(formItemContextKey, undefined)
const { proxy } = getCurrentInstance()
const emit = defineEmits(['update:modelValue'])
const number = ref(0)
const uploadList = ref([])
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const baseUrl = import.meta.env.VITE_APP_BASE_API
const uploadImgUrl = ref(import.meta.env.VITE_APP_BASE_API + '/common/upload') // 上传的图片服务器地址
const headers = ref({ Authorization: 'Bearer ' + getToken() })
const fileList = ref([])
const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize)
)

watch(() => props.modelValue, val => {
  if (val) {
    // 首先将值转为数组
    const list = Array.isArray(val) ? val : props.modelValue.split(',')
    // 然后将数组转为对象数组
    fileList.value = list.map(item => {
      if (typeof item === 'string') {
        // if (item.indexOf(baseUrl) === -1) {
        //   item = { name: baseUrl + item, url: baseUrl + item };
        // } else {
        //   item = { name: item, url: item };
        // }
        item = { name: item, url: item }
      }
      return item
    })
  } else {
    fileList.value = []
    return []
  }
}, { deep: true, immediate: true })

// 通过图片url获取在列表中的index
function getFileIndex(url) {
  const index = props.modelValue.findIndex(item => item === url)
  return index
}
// 左移图片
function handleMoveLeft(file) {
  const index = fileList.value.findIndex(item => item.url === file.url)
  const prevFile = fileList.value[index - 1]
  fileList.value[index - 1] = file
  fileList.value[index] = prevFile
  emit('update:modelValue', listToArray(fileList.value))
}
// 右移图片
function handleMoveRight(file) {
  const index = fileList.value.findIndex(item => item.url === file.url)
  const nextFile = fileList.value[index + 1]
  fileList.value[index + 1] = file
  fileList.value[index] = nextFile
  emit('update:modelValue', listToArray(fileList.value))
}
// 上传前loading加载
function handleBeforeUpload(file) {
  let isImg = false
  if (props.fileType.length) {
    let fileExtension = ''
    if (file.name.lastIndexOf('.') > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
    }
    isImg = props.fileType.some(type => {
      if (file.type.indexOf(type) > -1) return true
      if (fileExtension && fileExtension.indexOf(type) > -1) return true
      return false
    })
  } else {
    isImg = file.type.indexOf('image') > -1
  }
  if (!isImg) {
    proxy.$modal.msgError(
      `文件格式不正确, 请上传${props.fileType.join('/')}图片格式文件!`
    )
    return false
  }
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize
    if (!isLt) {
      proxy.$modal.msgError(`上传头像图片大小不能超过 ${props.fileSize} MB!`)
      return false
    }
  }
  proxy.$modal.loading('正在上传图片，请稍候...')
  number.value++
}

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`)
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  if (res.code === 200) {
    uploadList.value.push({ name: res.fileName, url: res.url })
    uploadedSuccessfully()
  } else {
    number.value--
    proxy.$modal.closeLoading()
    proxy.$modal.msgError(res.msg)
    proxy.$refs.imageUpload.handleRemove(file)
    uploadedSuccessfully()
  }
}

// 删除图片
function handleDelete(file) {
  const findex = fileList.value.map(f => f.name).indexOf(file.name)
  if (findex > -1 && uploadList.value.length === number.value) {
    fileList.value.splice(findex, 1)
    if (props.valueType === 'string') {
      emit('update:modelValue', listToString(fileList.value))
    } else {
      emit('update:modelValue', listToArray(fileList.value))
    }
    validateEl()
    return false
  }
}

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value)
    uploadList.value = []
    number.value = 0
    if (props.valueType === 'string') {
      emit('update:modelValue', listToString(fileList.value))
    } else {
      emit('update:modelValue', listToArray(fileList.value))
    }
    proxy.$modal.closeLoading()
    validateEl()
  }
}
// 使用elForm进行校验
function validateEl() {
  if (formContext && formItemContext) {
    formContext?.validateField([formItemContext.prop])
  }
}

// 上传失败
function handleUploadError() {
  proxy.$modal.msgError('上传图片失败')
  proxy.$modal.closeLoading()
}

// 预览
function handlePictureCardPreview(file) {
  dialogImageUrl.value = file.url
  dialogVisible.value = true
}

// 对象转成指定字符串分隔
function listToString(list, separator) {
  let strs = ''
  separator = separator || ','
  for (const i in list) {
    if (undefined !== list[i].url && list[i].url.indexOf('blob:') !== 0) {
      strs += list[i].url.replace(baseUrl, '') + separator
    }
  }
  return strs != '' ? strs.substr(0, strs.length - 1) : ''
}
// 对象转成图片路径数组
function listToArray(list) {
  return list.map(item => item.url)
}
</script>

<style scoped lang="scss">
.image-item{
  width: 100%;
  height: 100%;
}
:deep(.el-upload-list__item) {
  transition: none !important;
}
:deep(.el-upload-list--picture-card .el-upload-list__item){
  display: inline-block;
}
// .el-upload--picture-card 控制加号部分
:deep(.hide .el-upload--picture-card) {
    display: none;
}
.component-upload-image :deep(.el-upload-list__item-thumbnail){
  object-fit: cover;
}
</style>
