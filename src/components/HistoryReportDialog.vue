<template>
  <div class="dialog-mask">
    <div class="dialog-main" :style="mainStyle">
      <img class="dialog-bg" :src="bgImg" alt="bg">
      <div class="dialog-header">
        <span class="dialog-title">历史上报记录</span>
        <img
          class="dialog-close"
          :src="closeImg"
          alt="close"
          @click="$emit('close')"
        >
      </div>
      <div class="dialog-content">
        <div class="table-gradient-bg">
          <div class="table-toolbar">
            <el-button class="export-btn" size="small" @click="handleExport">导出</el-button>
          </div>
          <div class="table-scroll-area">
            <el-table :data="processedTableData" class="report-table">
              <el-table-column prop="deviceName" label="设备名称" min-width="120" />
              <el-table-column prop="imei" label="设备序列号(SN)" min-width="160" />
              <el-table-column label="监测值" min-width="160">
                <template #default="{ row }">
                  {{ row.labelTitle }}：{{ row.labelValue }}
                </template>
              </el-table-column>
              <!-- <el-table-column
                v-for="col in dynamicColumns"
                :key="col"
                :prop="col"
                :label="col"
                min-width="100"
              /> -->
              <el-table-column prop="alarmTime" label="上报时间" min-width="160" />
            </el-table>
          </div>
          <div class="table-pagination">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 30, 40]"
              layout="prev, pager, next, jumper, sizes"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, getCurrentInstance } from 'vue'
import bgImg from '@/assets/screen/dialog-back.png'
import closeImg from '@/assets/screen/dialog-close.png'
import { getCockpitIoteMessages } from '@/api/cockpit'

const props = defineProps({
  visible: Boolean,
  deviceId: [String, Number]
})
defineEmits(['close'])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const { proxy } = getCurrentInstance()

const processedTableData = ref([]) // 处理后的表格数据

const fetchData = async() => {
  if (!props.deviceId) return
  const params = {
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    deviceId: props.deviceId
  }
  try {
    const res = await getCockpitIoteMessages(params)
    // const rows = res.rows || []
    total.value = res.total || 0
    processedTableData.value = res.rows

    // // 1. 收集所有指标名
    // const labelSet = new Set()
    // rows.forEach(item => {
    //   if (item.labelTitle) labelSet.add(item.labelTitle)
    // })
    // dynamicColumns.value = Array.from(labelSet)

    // // 2. 按 deviceId+alarmTime 分组
    // const groupMap = new Map()
    // rows.forEach(item => {
    //   const key = `${item.deviceId}_${item.alarmTime}`
    //   if (!groupMap.has(key)) {
    //     groupMap.set(key, {
    //       deviceName: item.deviceName,
    //       imei: item.imei,
    //       alarmTime: item.alarmTime
    //     })
    //   }
    //   const row = groupMap.get(key)
    //   if (item.labelTitle) row[item.labelTitle] = item.labelValue
    // })
    // processedTableData.value = Array.from(groupMap.values())
  } catch (e) {
    processedTableData.value = []
    total.value = 0
  }
}

watch(() => props.deviceId, () => {
  currentPage.value = 1
  fetchData()
})
watch(currentPage, fetchData)
watch(pageSize, fetchData)

onMounted(fetchData)

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  fetchData()
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchData()
}

// 导出历史上报记录
const handleExport = () => {
  proxy.$modal.confirm('是否导出历史上报记录？', '提示', {
    confirmButtonText: '导出',
    cancelButtonText: '取消'
  }).then(() => {
    proxy.download('/cockpit/ioteMessages/export', {
      deviceId: props.deviceId
    }, `历史上报记录_${new Date().getTime()}.xlsx`)
  }).catch(() => {
    // 用户取消操作
  })
}

const mainStyle = {
  width: '1200px',
  height: '676px',
  position: 'relative',
  overflow: 'hidden',
  background: 'transparent',
  boxShadow: '0 0 24px #003366'
}
</script>

<style scoped>
.dialog-mask {
  position: absolute;
  left: 0; top: 0; right: 0; bottom: 0;
  z-index: 2000;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}
.dialog-main {
  position: relative;
  width: 1200px;
  height: 676px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
}
.dialog-bg {
  position: absolute;
  left: 0; top: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}
.dialog-header {
  position: relative;
  z-index: 2;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: #fff;
  font-weight: bold;
  letter-spacing: 4px;
  margin-bottom: 8px;
}
.dialog-title {
  flex: 1;
  text-align: center;
  padding-bottom: 10px;
}
.dialog-close {
  position: absolute;
  right: 32px;
  top: 16px;
  width: 32px;
  height: 32px;
  cursor: pointer;
  z-index: 3;
}
.dialog-content {
  position: relative;
  z-index: 2;
  flex: 1;
  padding: 24px 32px 16px 32px;
  display: flex;
  flex-direction: column;
  background: transparent;
}
.table-gradient-bg {
  background: linear-gradient(90deg, rgba(35,104,240,0.2) 0%, rgba(35,104,240,0.01) 100%);
  border-radius: 4px;
  padding: 16px 24px 0 24px;
  margin-bottom: 16px;
  width:100%;
  height: 564px;
  position: relative;
}
.table-scroll-area {
  height: calc(100% - 112px);
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.table-scroll-area::-webkit-scrollbar {
  display: none;
}
.table-toolbar {
  padding: 0 24px 14px 0px;
}
.report-table {
  margin: 0;
}
.table-pagination {
  position: absolute;
  right: 24px;
  bottom: 16px;
  margin: 0;
  display: flex;
  justify-content: flex-end;
}
.export-btn {
  width: 60px;
  height: 32px;
  background: #165DFF;
  color: #fff;
  border: none;
  font-size: 14px;
  padding: 0;
  border-radius: 4px;
  margin-right: 0;
}
/* 横线颜色彻底覆盖 */
:deep(.el-table__cell),
:deep(.el-table__header .el-table__cell),
:deep(.el-table__row .el-table__cell) {
  border-right: none !important;
  border-bottom: 1px solid #1346A6 !important;
}
:deep(.el-table__row:last-child .el-table__cell) {
  border-bottom: none !important;
}
/* 表头背景色 */
:deep(.el-table__header-wrapper),
:deep(.el-table__header),
:deep(.el-table__header th),
:deep(.el-table__header tr),
:deep(.el-table__header .el-table__cell) {
  background: #0D3581 !important;
  color: #fff !important;
}
/* 内容区背景彻底透明 */
:deep(.el-table),
:deep(.el-table__wrapper),
:deep(.el-table__body),
:deep(.el-table__row),
:deep(.el-table__cell),
:deep(.el-table__body-wrapper),
:deep(.el-table__inner-wrapper),
:deep(.el-table__content) {
  background: transparent !important;
  color: #fff !important;
}
/* 表格外边框颜色彻底覆盖 */
:deep(.el-table) {
  --el-table-border-color: #1346A6 !important;
  border: 1px solid #1346A6 !important;
  border-radius: 4px !important;
  box-shadow: none !important;
  background: transparent !important;
  border-bottom: none !important;
}
:deep(.el-table__wrapper),
:deep(.el-table__border),
:deep(.el-table__inner-wrapper),
:deep(.el-table__content) {
  border-color: #1346A6 !important;
  background: transparent !important;
}
/* 终极去除 el-table 底部白线 */
:deep(.el-table::after),
:deep(.el-table__inner-wrapper::after),
:deep(.el-table__body-wrapper::after),
:deep(.el-table__border::after),
:deep(.el-table__footer-wrapper::after),
:deep(.el-table__inner-wrapper),
:deep(.el-table__body-wrapper),
:deep(.el-table__border),
:deep(.el-table) {
  border-bottom: none !important;
  box-shadow: none !important;
}
</style>
