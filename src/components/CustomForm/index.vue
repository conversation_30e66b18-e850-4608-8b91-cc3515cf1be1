<template>
  <el-form
    ref="formRef"
    class="custom-form"
    :model="formData"
    v-bind="formProps"
  >
    <el-row :gutter="gutter">
      <template v-for="item in formConfig" :key="item.prop">
        <el-col v-if="item.upperTitle" :span="24">
          <slot v-if="slot['upper-title']" name="upper-title" :title="item.upperTitle" />
          <div v-else class="form-title">{{ item.upperTitle }}</div>
        </el-col>
        <el-col
          :span="item.rowProps?.span || span"
          v-bind="item.rowProps"
        >
          <el-form-item
            :label="item.label"
            :prop="item.prop"
            :rules="item.rules"
            v-bind="item.formItemProps"
          >
            <template v-if="item.slotName">
              <slot :name="item.slotName" />
            </template>
            <!-- 输入框 -->
            <el-input
              v-if="item.type === 'input'"
              v-model="formData[item.prop]"
              :placeholder="item.eleProps?.placeholder || '请输入'"
              :clearable="item.eleProps?.clearable || true"
              v-bind="item.elProps"
            />
            <!-- 数字输入框 -->
            <el-input-number v-if="item.type === 'number'" v-model="formData[item.prop]" v-bind="item.elProps" />
            <!-- 下拉框 -->
            <el-select
              v-if="item.type === 'select'"
              v-model="formData[item.prop]"
              :placeholder="item.eleProps?.placeholder || '请选择'"
              :clearable="item.eleProps?.clearable || true"
              v-bind="item.elProps"
            >
              <el-option
                v-for="ele in item.options || optionsObj[item.prop]"
                :key="ele.value"
                :label="ele.label"
                :value="ele.value"
                v-bind="ele.elProps"
              />
            </el-select>
            <!-- 开关 -->
            <el-switch v-if="item.type === 'switch'" v-model="formData[item.prop]" v-bind="item.elProps" />
            <!-- 单选框 -->
            <el-radio-group v-if="item.type === 'radio'" v-model="formData[item.prop]" v-bind="item.elProps">
              <el-radio
                v-for="ele in item.options || optionsObj[item.prop]"
                :key="ele.value"
                :value="ele.value"
                v-bind="ele.elProps"
              >{{ ele.label }}</el-radio>
            </el-radio-group>
            <!-- 复选框 -->
            <el-checkbox-group v-if="item.type === 'checkbox'" v-model="formData[item.prop]" v-bind="item.elProps">
              <el-checkbox
                v-for="ele in item.options || optionsObj[item.prop]"
                :key="ele.value"
                :value="ele.value"
                :label="ele.label"
                v-bind="ele.elProps"
              />
            </el-checkbox-group>
            <!-- 单个/多个日期选择（多个日期的defaultValue为数组） -->
            <el-date-picker
              v-if="item.type === 'date'"
              v-model="formData[item.prop]"
              :type="item.range ? 'daterange' : 'date'"
              :clearable="item.eleProps?.clearable || true"
              :value-format="item.eleProps?.format || 'YYYY-MM-DD'"
              :placeholder="item.eleProps?.placeholder || '请选择'"
              v-bind="item.elProps"
            />
            <!-- 单个/多个日期时间选择（多个日期时间的defaultValue为数组） -->
            <el-date-picker
              v-if="item.type === 'datetime'"
              v-model="formData[item.prop]"
              :type="item.range ? 'datetimerange' : 'datetime'"
              :clearable="item.eleProps?.clearable || true"
              :value-format="item.eleProps?.format || 'YYYY-MM-DD HH:mm:ss'"
              :placeholder="item.eleProps?.placeholder || '请选择'"
              v-bind="item.elProps"
            />
            <!-- 文件上传 -->
            <source-upload v-if="item.type === 'file'" v-model="formData[item.prop]" v-bind="item.elProps" />
            <!-- 图片上传（多张图片valute-type为数组，单张图片valute-type可为字符串或数组，为string时可不传） -->
            <image-upload
              v-if="item.type === 'pic'"
              v-model="formData[item.prop]"
              v-bind="item.elProps"
            />
            <!-- 富文本 -->
            <lonely-editor v-if="item.type === 'editor'" v-model="formData[item.prop]" v-bind="item.elProps" />
          </el-form-item>
        </el-col>
      </template>

    </el-row>
    <el-form-item>
      <el-button type="primary" @click="handleSubmit">提交</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup name="CustomForm">
import { useVModel } from '@vueuse/core'
import SourceUpload from '@/components/SourceUpload'

const slot = useSlots()
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  formConfig: {
    type: Array,
    required: true
  },
  formProps: {
    type: Object,
    default: () => ({})
  },
  gutter: {
    type: Number,
    default: 20
  },
  span: { // el-col的span属性，优化使用内部的span，若没有再使用这里
    type: Number,
    default: 24
  },
  optionsObj: {
    type: Object,
    default: () => ({})
  }
})
const emit = defineEmits(['update:modelValue', 'on-success', 'on-error'])
// 表单数据
const formData = useVModel(props, 'modelValue', emit)

const formRef = ref()
// 初始化表单数据（当组件加载时，需要先对表单数据进行初始化）
function initFormData() {
  props.formConfig.forEach(item => {
    formData.value[item.prop] = item.defaultValue
  })
}
// 提交表单数据
function handleSubmit() {
  formRef.value.validate(async valid => {
    console.log(`output->valid`, valid)
    if (valid) {
      // 若校验通过，则告诉父组件可以提交表单数据
      emit('on-success')
    } else {
      emit('on-error')
    }
  })
}

initFormData()
</script>

<style lang="scss" scoped>
.custom-form{
  .form-title{
    font-size: 16px;
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    &::before{
      content: '';
      display: block;
      background-color: #409EFF;
      width: 3px;
      height: 16px;
      border-radius: 2px;
      margin-right: 8px;
    }
  }
}
</style>
