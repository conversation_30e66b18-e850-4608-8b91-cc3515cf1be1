export const formConfig = [
  {
    label: '标题',
    type: 'input',
    prop: 'title',
    defaultValue: 'This is title',
    placeholder: '请输入标题',
    rules: [
      {
        required: true,
        message: '请输入标题',
        trigger: 'blur'
      }
    ]
  },
  {
    label: '类型',
    type: 'select',
    prop: 'infoType',
    placeholder: '请选择类型',
    defaultValue: 2,
    options: [
      {
        label: '类型1',
        value: 1
      },
      {
        label: '类型2',
        value: 2
      }
    ]
  },
  {
    label: '置顶',
    type: 'switch',
    prop: 'top',
    defaultValue: false,
    elProps: {
      activeText: 'yes',
      inactiveText: 'no'
    }
  },
  {
    label: '性别',
    type: 'radio',
    prop: 'gender',
    defaultValue: 'female',
    options: [
      {
        label: '男',
        value: 'male',
        elProps: {
          disabled: true
        }
      },
      {
        label: '女',
        value: 'female'
      }
    ]
  },
  {
    label: '喜欢的运动',
    type: 'checkbox',
    prop: 'sport',
    options: [
      {
        label: '篮球',
        value: 'basketball',
        elProps: {
          disabled: true
        }
      },
      {
        label: '足球',
        value: 'football'
      },
      {
        label: '跑步',
        value: 'running'
      }
    ]
  },
  {
    label: '出生日期',
    type: 'date',
    range: false,
    prop: 'birthday',
    defaultValue: '2023-01-01',
    elProps: {}
  },
  {
    label: '合同日期',
    type: 'date',
    range: true,
    prop: 'dateRange',
    elProps: {
      startPlaceholder: '合同开始日期',
      endPlaceholder: '合同结束日期'
    }
  },
  {
    label: '单个时间',
    type: 'datetime',
    range: false,
    prop: 'datetime',
    defaultValue: '2023-01-01 12:23:43',
    elProps: {}
  },
  {
    label: '多个时间',
    type: 'datetime',
    range: true,
    prop: 'datetimeRange',
    elProps: {
      startPlaceholder: '合同开始时间',
      endPlaceholder: '合同结束时间'
    }
  },
  {
    label: '图片上传',
    type: 'pic',
    prop: 'pic',
    defaultValue: undefined,
    rules: [
      {
        required: true,
        message: '请上传图片',
        trigger: 'blur'
      }
    ],
    elProps: {
      limit: 1,
      fileSize: 50,
      valueType: 'string'
    }
  },
  {
    label: '多张图片上传',
    type: 'pic',
    prop: 'pics',
    defaultValue: ['http://219.152.95.18:8077/profile/upload/2024/07/25/路飞111_20240725140741A011.jpg', 'http://219.152.95.18:8077/profile/upload/2024/07/25/路飞111_20240725140741A011.jpg'],
    rules: [
      {
        required: true,
        message: '请上传图片',
        trigger: 'blur'
      }
    ],
    elProps: {
      limit: 5,
      fileSize: 50,
      valueType: 'array'
    }
  },
  {
    lable: '富文本',
    type: 'editor',
    prop: 'content',
    defaultValue: '',
    elProps: {
      height: 300
    }
  }
]
