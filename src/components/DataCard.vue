<template>
  <div class="data-card">
    <div class="card-content">
      <div class="icon-wrapper">
        <img :src="iconSrc || defaultIconSrc" alt="data icon" class="data-icon">
      </div>
      <div class="number-wrapper">
        <span class="number">{{ number }}</span>
        <span class="unit">{{ unit }}</span>
      </div>
    </div>
    <div class="card-footer">
      <img src="@/assets/screen/right-arrow.svg" alt="data icon" class="data-icon">
      <span class="card-title">{{ title }}</span>
      <img src="@/assets/screen/left-arrow.svg" alt="data icon" class="data-icon">
    </div>
  </div>
</template>

<script setup>
import defaultIcon from '@/assets/screen/data-icon.png'

defineProps({
  title: {
    type: String,
    required: true
  },
  number: {
    type: [Number, String],
    required: true
  },
  unit: {
    type: String,
    default: '台'
  },
  iconSrc: {
    type: String,
    default: ''
  }
})

const defaultIconSrc = defaultIcon
</script>

<style scoped>
.data-card {
  width: 452px;
  height: 137px;
  background: url(@/assets/screen/data-bg.png) no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
}

.card-content {
  flex: 1;
  height: 81px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 16px;
}

.icon-wrapper {
  width: 56px;
  height: 56px;
}

.data-icon {
  width: 57px;
  height: 57px;
  object-fit: contain;
}

.number-wrapper {
  display: flex;
  align-items: center;
}

.number {
  font-family: 'DINPro';
  font-size: 40px;
  font-weight: 700;
  background: linear-gradient(180deg, rgba(244, 251, 255, 1) 0%, rgba(60, 193, 255, 1) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1;
}

.unit {
  font-family: 'PingFang SC';
  font-size: 28px;
  color: #FFFFFF;
  margin-left: 4px;
}

.card-footer {
  height: 56px;
  display: flex;
  padding: 8px;
  align-items: center;
  justify-content: center;
}

.card-title {
  font-family: "Alimama ShuHeiTi";
  font-size: 20px;
  font-weight: 700;
  color: #D0DEEE;
}
</style>
