<template>
  <div>
    <template v-for="(item, index) in options">
      <template v-if="values.includes(item.value)">
        <span
          v-if="showDot"
          :key="'dot-' + item.value"
          class="dict-tag"
          :index="index"
        >
          <span class="dict-dot" :style="{width: item.dotSize, height: item.dotSize, backgroundColor: item.dotColor}" />
          {{ item.label }}
        </span>
        <span
          v-else-if="item.elTagType == 'default' || item.elTagType == ''"
          :key="item.value"
          :index="index"
          :class="item.elTagClass"
        >{{ item.label }}</span>
        <el-tag
          v-else
          :key="item.value + ''"
          :disable-transitions="true"
          :index="index"
          :type="item.elTagType"
          :class="item.elTagClass"
        >
          {{ item.label }}
        </el-tag>
      </template>
    </template>
  </div>
</template>

<script setup>
const props = defineProps({
  // 数据
  options: {
    type: Array,
    default: null
  },
  // 当前的值
  value: [Number, String, Array],
  showDot: {
    type: Boolean,
    default: false
  }
})

const values = computed(() => {
  if (props.value !== null && typeof props.value !== 'undefined') {
    return Array.isArray(props.value) ? props.value : [String(props.value)]
  } else {
    return []
  }
})

</script>

<style scoped>
.dict-tag{
  display: flex;
  align-items: center;
}
.dict-dot{
  display: inline-block;
  margin-right: 10px;
  border-radius: 50%;
}
.el-tag + .el-tag {
  margin-left: 10px;
}
</style>
