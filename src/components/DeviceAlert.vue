<template>
  <div class="device-alert">
    <!-- Tab选择区 -->
    <div class="tab-container">
      <div
        v-for="tab in tabs"
        :key="tab.key"
        :class="['tab-item', { active: activeTab === tab.key }]"
        @click="handleTabChange(tab.key)"
      >
        {{ tab.label }}({{ tab.count }})
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        ref="tableRef"
        :data="showTableData"
        :header-cell-style="headerStyle"
        :cell-style="cellStyle"
        :row-style="{ minHeight: '40px'}"
        :show-header="true"
        :border="false"
        :highlight-current-row="true"
        height="100%"
        class="custom-table"
        @current-change="handleCurrentChange"
        @row-click="handleRowClick"
      >
        <el-table-column prop="deviceId" label="设备编号" width="132" />
        <el-table-column prop="deviceName" label="设备名称" width="110" />
        <el-table-column prop="houseAddress" label="设备位置" min-width="108">
          <template #default="scope">
            <div class="address-cell">{{ scope.row.houseAddress }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="发生时间" min-width="110" />
        <!-- <el-table-column
          prop="handleStatus"
          label="状态"
          width="100"
        >
          <template #default="scope">
            <span
              class="status-dot"
              :class="isHandled(scope.row) ? 'dot-handled' : 'dot-unhandled'"
            />
            <span style="margin-left: 6px; color: #fff;">
              {{ isHandled(scope.row) ? '已处理' : '未处理' }}
            </span>
          </template>
        </el-table-column> -->
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { getCockpitAlarmList } from '@/api/cockpit'
import { useRouter } from 'vue-router'

// 添加路由实例
const router = useRouter()

// 不再使用props，直接从API获取数据
const activeTab = ref('all')
const activeRow = ref(0)
const tableRef = ref(null)
let scrollTimer = null
const currentIndex = ref(0)
const alarmList = ref([])
const loading = ref(false)

// 判断设备是否已处理
const isHandled = (row) => {
  // 同时兼容status和handleStatus两种字段
  return row.status === '0' || row.handleStatus === true
}

// 获取告警列表数据 - 只获取全部数据
const fetchAlarmList = async() => {
  loading.value = true
  try {
    const params = { pageSize: 10000, pageNum: 1 }
    const res = await getCockpitAlarmList(params)
    if (res.code === 200) {
      alarmList.value = res.rows || []
    }
  } catch (error) {
    console.error('获取告警列表失败', error)
  } finally {
    loading.value = false
  }
}

// 处理标签切换 - 不再重新获取数据，只切换显示
const handleTabChange = (tabKey) => {
  activeTab.value = tabKey

  // 重置滚动位置
  currentIndex.value = 0
  if (tableRef.value) tableRef.value.setScrollTop(0)
}

// 添加行点击事件处理函数
const handleRowClick = (row) => {
  // 停止自动滚动
  if (scrollTimer) {
    clearInterval(scrollTimer)
    scrollTimer = null
  }

  // 跳转到告警管理页面并传递参数
  router.push({
    path: '/alarmManage',
    query: {
      deviceName: row.deviceName,
      handleStatus: isHandled(row).toString(),
      logId: row.logId
    }
  })
}

const allList = computed(() => alarmList.value)
const handledList = computed(() => alarmList.value.filter(item => isHandled(item)))
const unhandledList = computed(() => alarmList.value.filter(item => !isHandled(item)))

const tabs = computed(() => [
  { key: 'all', label: '全部', count: allList.value.length },
  { key: 'handled', label: '已处理', count: handledList.value.length },
  { key: 'unhandled', label: '未处理', count: unhandledList.value.length }
])

const showTableData = computed(() => {
  let list = []
  if (activeTab.value === 'all') list = allList.value
  else if (activeTab.value === 'handled') list = handledList.value
  else if (activeTab.value === 'unhandled') list = unhandledList.value
  return list
})

const headerStyle = {
  background: 'transparent',
  backgroundColor: 'transparent',
  color: 'rgba(255, 255, 255, 0.8)',
  fontSize: '14px',
  fontWeight: 'normal',
  padding: '0 12px',
  textAlign: 'center',
  height: '40px',
  borderRight: 'none'
}

const cellStyle = {
  color: 'rgba(255, 255, 255, 0.6)',
  fontSize: '12px',
  background: 'transparent',
  borderBottom: '1px solid rgba(167, 199, 251, 0.3)',
  padding: '0 12px',
  textAlign: 'center'
}

const handleCurrentChange = (row) => {
  if (row) {
    activeRow.value = showTableData.value.indexOf(row)
  }
}

const startScroll = () => {
  if (!tableRef.value || showTableData.value.length <= 0) return

  scrollTimer = setInterval(() => {
    if (showTableData.value.length <= 0) return

    if (currentIndex.value >= showTableData.value.length - 1) {
      currentIndex.value = 0
      tableRef.value.setScrollTop(0)
    } else {
      currentIndex.value++
      // 使用scrollToRow方法代替固定高度的滚动
      tableRef.value.scrollToRow(currentIndex.value)
    }
  }, 3000)
}

onMounted(() => {
  // 初始加载全部数据
  fetchAlarmList()
  startScroll()
})

onUnmounted(() => {
  if (scrollTimer) {
    clearInterval(scrollTimer)
  }
})

watch(() => alarmList.value, () => {
  if (scrollTimer) {
    clearInterval(scrollTimer)
  }
  currentIndex.value = 0
  if (tableRef.value) tableRef.value.setScrollTop(0)
  startScroll()
}, { deep: true })
</script>

<style scoped>
.device-alert {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

/* 添加一个类用于覆盖表头样式 */
:deep(.custom-table .el-table__header) {
  background: url(@/assets/screen/table-head-bg.png) no-repeat !important;
  background-size: 100% 100% !important;
}

:deep(.custom-table .el-table__header-wrapper th.el-table__cell) {
  background-color: transparent !important;
  border-right: none !important;
}

.tab-container {
  display: flex;
  gap: 10px;
  margin: 5px 0;
  height: 32px;
  flex-shrink: 0;
}

.tab-item {
  width: 99px;
  height: 32px;
  padding: 0;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  border-radius: 2px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(35, 104, 240, 0.5);
  box-sizing: border-box;
}

.tab-item.active {
  background: linear-gradient(to top, rgba(35, 104, 240, 0.56) 0%, rgba(35, 104, 240, 0) 100%);
  color: #C2DFFF;
  border: 1px solid rgb(35, 104, 240);
}

.table-container {
  flex: 1;
  overflow: hidden;
  width: 100%;
  position: relative;
  height: calc(100% - 42px); /* 42px是tab-container的高度加上margin */
}

:deep(.el-table) {
  background: transparent !important;
  width: 100% !important;
  table-layout: fixed !important;
  height: 100% !important;
}

:deep(.el-table__inner-wrapper) {
  height: 100% !important;
}

:deep(.el-table__inner-wrapper::before),
:deep(.el-table__border-left-patch) {
  display: none;
}

:deep(.el-table__row) {
  background: transparent !important;
}

:deep(.el-table__row:hover) {
  background: linear-gradient(90deg, rgba(35, 66, 127, 0.8) 0%, rgba(35, 66, 127, 0) 100%) !important;
}

:deep(.el-table__row.current-row) {
  background: url(@/assets/screen/data-selected.png) no-repeat !important;
  background-size: 100% 100% !important;
}

:deep(.el-table__header-wrapper) {
  border-bottom: none;
}

:deep(.el-table__header-wrapper .el-table__header) {
  border-bottom: none;
  width: 100%;
}

:deep(.el-table__header-wrapper table) {
  background: transparent !important;
}

:deep(.el-table__header-row) {
  background-color: transparent !important;
}

:deep(.el-table tr) {
  background-color: transparent !important;
}

:deep(.el-table__body-wrapper) {
  height: calc(100% - 40px) !important; /* 减去表头高度 */
  overflow-y: auto !important;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background-color: rgba(35, 104, 240, 0.3);
  border-radius: 2px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: rgba(35, 104, 240, 0.5);
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell) {
  background: transparent !important;
}

:deep(.el-table__header-wrapper th.el-table__cell) {
  background: transparent !important;
  background-color: transparent !important;
  border-bottom: none !important;
  border-right: none !important;
  text-align: center !important;
  color: rgba(255, 255, 255, 0.8) !important;
  height: 40px !important;
}

:deep(.el-table__cell) {
  border-bottom: 1px solid rgba(167, 199, 251, 0.3) !important;
}

:deep(.el-table__header .el-table__cell) {
  border-right: none !important;
}

:deep(.el-table__body tr:last-child td.el-table__cell) {
  border-bottom: 1px solid rgba(167, 199, 251, 0.3) !important;
}

:deep(.el-table .cell) {
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 设备位置列样式 - 允许文本换行显示 */
.address-cell {
  white-space: normal !important;
  word-break: break-all !important;
  word-wrap: break-word !important;
  line-height: 1.5;
  text-align: center;
  overflow: visible !important;
}

:deep(.address-cell .cell) {
  white-space: normal !important;
  word-break: break-all !important;
  word-wrap: break-word !important;
  text-overflow: clip !important;
  overflow: visible !important;
}

:deep(.el-table__header .cell) {
  color: rgba(255, 255, 255, 0.8) !important;
  font-weight: bold;
  font-size: 14px;
  line-height: 40px;
  border-right: none !important;
}

:deep(.el-table__row.current-row td.el-table__cell) {
  color: #00FFFF !important;
}

.status-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  vertical-align: middle;
  margin-right: 4px;
}
.dot-unhandled {
  background: #F53F3F;
}
.dot-handled {
  background: #C9CDD4;
}
</style>
