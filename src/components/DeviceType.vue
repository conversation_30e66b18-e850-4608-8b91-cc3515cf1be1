<template>
  <div class="device-type">
    <div class="pie-chart">
      <div class="circle-bg">
        <div ref="chartRef" class="chart" />
        <div class="total-info">
          <div class="total-num">{{ total }}</div>
          <div class="total-text">设备总数<br>(台)</div>
        </div>
      </div>
    </div>
    <div class="type-list">
      <div v-for="(item, index) in deviceTypes" :key="item.str" class="type-item">
        <div class="type-bg">
          <div class="type-left">
            <span class="type-icon" :style="{ backgroundColor: colors[index] }" />
            <span class="type-name">{{ item.str }}</span>
          </div>
          <span class="type-value">{{ item.num }}台</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  deviceTypes: {
    type: Array,
    default: () => [
      { str: '裂缝监测仪', num: 86 },
      { str: '倾角监测仪', num: 13 },
      { str: '沉降监测仪', num: 48 },
      { str: '内部位移传感器', num: 25 },
      { str: '其他设备', num: 8 }
    ]
  }
})
const chartRef = ref(null)
let chart = null

const colors = ['#05CCF3', '#207EE7', '#DAB165', '#67C669', '#AEC2C5']

const total = computed(() => {
  return props.deviceTypes.reduce((sum, item) => sum + item.num, 0)
})

const chartData = computed(() => {
  return props.deviceTypes.map(item => ({
    name: item.str,
    value: item.num
  }))
})

const initChart = () => {
  if (chart) {
    chart.dispose()
  }

  chart = echarts.init(chartRef.value)
  const option = {
    color: colors,
    series: [
      {
        type: 'pie',
        radius: ['60%', '75%'],
        center: ['50%', '50%'],
        data: chartData.value,
        label: {
          show: false
        },
        emphasis: {
          scale: false
        }
      }
    ]
  }
  chart.setOption(option)
}

onMounted(() => {
  setTimeout(() => {
    initChart()
  }, 500)
})

onUnmounted(() => {
  if (chart) {
    chart.dispose()
  }
})
</script>

<style scoped>
.device-type {
  height: 100%;
  display: flex;
  align-items: center;
  gap: 66px;
}

.pie-chart {
  position: relative;
  width: 140px;
  height: 140px;
  flex-shrink: 0;
}

.circle-bg {
  width: 100%;
  height: 100%;
  background: url(@/assets/screen/circle-bg.png) no-repeat center;
  background-size: contain;
  position: relative;
}

.chart {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.total-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #FFFFFF;
}

.total-num {
  font-family: 'DINPro';
  font-size: 16px;
  font-weight: bold;
  background: linear-gradient(180deg, #FFFFFF 0%, #7CD7FF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1;
}

.total-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 4px;
}

.type-list {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(2, 170px);
  gap: 10px 20px;
  align-content: center;
}

.type-item {
  width: 170px;
  height: 24px;
}

.type-bg {
  width: 100%;
  height: 100%;
  background: url(@/assets/screen/pie-text-bg.png) no-repeat;
  background-size: 100% 100%;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.type-left {
  display: flex;
  align-items: center;
  gap: 4px;
}

.type-icon {
  width: 8px;
  height: 8px;
  border-radius: 2px;
}

.type-name {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.type-value {
  font-family: 'DINPro';
  font-size: 12px;
  color: #D0DEEE;
}
</style>
