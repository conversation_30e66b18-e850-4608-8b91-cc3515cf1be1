<!-- eslint-disable vue/v-on-event-hyphenation -->
<template>
  <div style="border: 1px solid #ccc;">
    <toolbar
      style="border-bottom: 1px solid #ccc;"
      :editor="editorRef"
      :default-config="toolbarConfig"
      :mode="mode"
    />
    <editor
      v-model="html"
      :style="{height: height + 'px', 'overflow-y': 'hidden'}"
      :default-config="editorConfig"
      :mode="mode"
      @onCreated="onCreated"
    />
  </div>
</template>

<script setup name="LonelyEditor">
import '@wangeditor/editor/dist/css/style.css'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { getToken } from '@/utils/auth'
import { reactive } from 'vue'

const imageSize = 50 * 1024 * 1024
const videoSize = 300 * 1024 * 1024
const { proxy } = getCurrentInstance()

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  height: {
    type: Number,
    default: 500
  },
  readonly: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:modelValue'])

const editorRef = shallowRef(null)
const toolbarConfig = reactive({
  excludeKeys: ['emotion'] // 排除掉表情
})
const editorConfig = computed(() => {
  return {
    placeholder: '请输入内容...',
    readOnly: props.readonly,
    MENU_CONF: {
      uploadImage: {
        server: import.meta.env.VITE_APP_BASE_API + '/common/upload',
        fieldName: 'file',
        headers: {
          Authorization: 'Bearer ' + getToken()
        },
        maxFileSize: imageSize,
        onBeforeUpload: (file) => {
          const limitSize = imageSize
          const data = Object.entries(file)[0][1]
          const isLt = data.size < limitSize
          if (!isLt) {
            proxy.$modal.msgError(`上传图片大小不能超过 ${limitSize} MB!`)
            return false
          }
          return file
        },
        customInsert: (res, insertFn) => {
          const { code, msg } = res
          if (code === 200) {
            insertFn(res.url, res.originalFilename)
          } else {
            proxy.$modal.msgError(msg || '图片上传失败')
          }
        }
      },
      uploadVideo: {
        server: import.meta.env.VITE_APP_BASE_API + '/common/upload',
        fieldName: 'file',
        headers: {
          Authorization: 'Bearer ' + getToken()
        },
        maxFileSize: videoSize,
        onBeforeUpload: (file) => {
          const limitSize = videoSize
          const data = Object.entries(file)[0][1]
          const isLt = data.size < limitSize
          if (!isLt) {
            proxy.$modal.msgError(`上传视频大小不能超过 ${limitSize} MB!`)
            return false
          }
          return file
        },
        customInsert: (res, insertFn) => {
          const { code, msg } = res
          if (code === 200) {
            insertFn(res.url)
          } else {
            proxy.$modal.msgError(msg || '视频上传失败')
          }
        }
      }
    }
  }
})
const mode = ref('default')

const html = computed({
  get() {
    // 当后端没传值时，需要默认为''
    return props.modelValue || ''
  },
  set(val) {
    emit('update:modelValue', val)
  }
})

function onCreated(editor) {
  editorRef.value = editor
}

onBeforeUnmount(() => {
  const editor = editorRef.value
  editor && editor.destroy()
})

</script>

<style>
.w-e-text-placeholder{
  line-height: 1.5;
}
.w-e-full-screen-container{
  z-index: 9999;
}
</style>
