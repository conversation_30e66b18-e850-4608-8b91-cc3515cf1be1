import request from '@/utils/request'

// 获取房屋列表
export function listHouse(params) {
  return request({
    url: '/houseInfo/list',
    method: 'get',
    params
  })
}
export function listHouseTree(params) {
  return request({
    url: '/deviceInfo/getHouseInfoTree',
    method: 'get',
    params
  })
}

// 获取房屋详情
export function getHouseDetail(id) {
  return request({
    url: `/houseInfo/${id}`,
    method: 'get'
  })
}

// 新增房屋
export function addHouse(data) {
  return request({
    url: '/houseInfo',
    method: 'post',
    data
  })
}

// 修改房屋
export function updateHouse(data) {
  return request({
    url: '/houseInfo',
    method: 'put',
    data
  })
}

// 启停房屋
export function switchHouse(id) {
  return request({
    url: `/houseInfo/switch/${id}`,
    method: 'put'
  })
}

// 导出房屋列表
export function exportHouseList() {
  return request({
    url: '/houseInfo/export',
    method: 'post',
    responseType: 'blob'
  })
}
