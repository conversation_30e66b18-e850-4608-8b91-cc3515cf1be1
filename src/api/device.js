import request from '@/utils/request'

// 获取设备列表
export function getDeviceList(params) {
  return request({
    url: '/deviceInfo/list',
    method: 'get',
    params
  })
}

// 获取设备详情
export function getDeviceDetail(id) {
  return request({
    url: `/deviceInfo/${id}`,
    method: 'get'
  })
}

// 新增设备
export function addDevice(data) {
  return request({
    url: '/deviceInfo',
    method: 'post',
    data
  })
}

// 更新设备
export function updateDevice(data) {
  return request({
    url: '/deviceInfo',
    method: 'put',
    data
  })
}

// 删除设备
export function deleteDevice(id) {
  return request({
    url: `/deviceInfo/${id}`,
    method: 'delete'
  })
}

// 启停设备信息
export function switchDevice(id) {
  return request({
    url: `/deviceInfo/switch/${id}`,
    method: 'put'
  })
}

// 导出设备信息列表
export function exportDeviceList() {
  return request({
    url: '/deviceInfo/export',
    method: 'post',
    responseType: 'blob'
  })
}
