const houseList = [
  {
    id: '1',
    houseId: 'H20240301001',
    belongUnit: '重庆市沙坪坝区人民政府',
    houseName: '沙坪坝区政府办公大楼',
    houseAddress: '重庆市沙坪坝区凤天大道8号',
    owner: '张三',
    ownerPhone: '13812345678',
    joinTime: '2024-03-01',
    status: true
  },
  {
    id: '2',
    houseId: 'H20240301002',
    belongUnit: '重庆市沙坪坝区教育委员会',
    houseName: '沙坪坝区教育大厦',
    houseAddress: '重庆市沙坪坝区小龙坎正街',
    owner: '李四',
    ownerPhone: '13912345678',
    joinTime: '2024-03-01',
    status: true
  },
  {
    id: '3',
    houseId: 'H20240301003',
    belongUnit: '重庆市沙坪坝区卫生健康委员会',
    houseName: '沙坪坝区卫生大楼',
    houseAddress: '重庆市沙坪坝区渝碚路',
    owner: '王五',
    ownerPhone: '13712345678',
    joinTime: '2024-03-02',
    status: true
  },
  {
    id: '4',
    houseId: 'H20240301004',
    belongUnit: '重庆市沙坪坝区公安分局',
    houseName: '沙坪坝区公安分局办公楼',
    houseAddress: '重庆市沙坪坝区天陈路',
    owner: '赵六',
    ownerPhone: '13612345678',
    joinTime: '2024-03-02',
    status: false
  },
  {
    id: '5',
    houseId: 'H20240301005',
    belongUnit: '重庆市沙坪坝区文化旅游委员会',
    houseName: '沙坪坝区文化中心',
    houseAddress: '重庆市沙坪坝区文化广场',
    owner: '钱七',
    ownerPhone: '13512345678',
    joinTime: '2024-03-03',
    status: true
  }
]

export default [
  // 查询房屋列表
  {
    url: '/house/list',
    method: 'get',
    response: (config) => {
      const { houseName, status, pageNum, pageSize } = config.query
      let mockList = [...houseList]

      // 根据房屋名称筛选
      if (houseName) {
        mockList = mockList.filter(item => item.houseName.includes(houseName))
      }

      // 根据状态筛选
      if (status !== '' && status !== undefined) {
        mockList = mockList.filter(item => item.status === (status === 'true' || status === true))
      }

      // 分页
      const startIndex = (pageNum - 1) * pageSize
      const endIndex = startIndex + parseInt(pageSize)
      const pageList = mockList.slice(startIndex, endIndex)

      return {
        code: 200,
        message: 'success',
        data: {
          total: mockList.length,
          list: pageList
        }
      }
    }
  },

  // 获取房屋详情
  {
    url: '/house/[\\d]+',
    method: 'get',
    response: (config) => {
      const id = config.url.match(/\/house\/(\d+)/)[1]
      const house = houseList.find(item => item.id === id)

      return {
        code: 200,
        message: 'success',
        data: house || null
      }
    }
  },

  // 更新房屋状态
  {
    url: '/house/status',
    method: 'put',
    response: (config) => {
      const { id, status } = config.body
      const house = houseList.find(item => item.id === id)
      if (house) {
        house.status = status
      }

      return {
        code: 200,
        message: 'success'
      }
    }
  },

  // 新增房屋
  {
    url: '/house',
    method: 'post',
    response: (config) => {
      const house = config.body
      house.id = String(houseList.length + 1)
      house.joinTime = new Date().toISOString().split('T')[0]
      houseList.push(house)

      return {
        code: 200,
        message: 'success'
      }
    }
  },

  // 修改房屋
  {
    url: '/house',
    method: 'put',
    response: (config) => {
      const updateHouse = config.body
      const index = houseList.findIndex(item => item.id === updateHouse.id)
      if (index !== -1) {
        Object.assign(houseList[index], updateHouse)
      }

      return {
        code: 200,
        message: 'success'
      }
    }
  },

  // 删除房屋
  {
    url: '/house/[\\d]+',
    method: 'delete',
    response: (config) => {
      const id = config.url.match(/\/house\/(\d+)/)[1]
      const index = houseList.findIndex(item => item.id === id)
      if (index !== -1) {
        houseList.splice(index, 1)
      }

      return {
        code: 200,
        message: 'success'
      }
    }
  }
]
