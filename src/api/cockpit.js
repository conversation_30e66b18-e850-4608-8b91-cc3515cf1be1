import request from '@/utils/request'

// 查询告警记录列表
export function getCockpitAlarmList(params) {
  return request({
    url: '/cockpit/alarmList',
    method: 'get',
    params
  })
}

// 设备数量
export function getCockpitDeviceCount(params) {
  return request({
    url: '/cockpit/deviceCount',
    method: 'get',
    params
  })
}

// 查询设备信息列表
export function getCockpitDeviceList(params) {
  return request({
    url: '/cockpit/deviceList',
    method: 'get',
    params
  })
}

// 设备类型
export function getCockpitDeviceType(params) {
  return request({
    url: '/cockpit/deviceType',
    method: 'get',
    params
  })
}

// 历史上报记录（占位，弹窗未做）
export function getCockpitIoteMessages(params) {
  return request({
    url: '/cockpit/ioteMessages',
    method: 'get',
    params
  })
}
