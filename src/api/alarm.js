import request from '@/utils/request'

// 查询告警列表
export function getAlarmList(params) {
  return request({
    url: '/alarmLog/list',
    method: 'get',
    params
  })
}

// 获取告警详情
export function getAlarmDetail(logId) {
  return request({
    url: `/alarmLog/${logId}`,
    method: 'get'
  })
}

// 处理告警
export function processAlarm(data) {
  return request({
    url: '/alarmLog',
    method: 'put',
    data
  })
}

// 导出告警记录列表
export function exportAlarmList(params) {
  return request({
    url: '/alarmLog/export',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}
