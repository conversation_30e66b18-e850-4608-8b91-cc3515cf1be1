<template>
  <div class="device-info device-info-select-scope">
    <!-- 搜索区域 -->
    <div class="search-container">
      <div class="search-item">
        <span class="label">设备名称</span>
        <el-input v-model="searchForm.deviceName" placeholder="请输入" class="search-input" />
      </div>
      <div class="search-item">
        <span class="label">组织筛选</span>
        <el-select v-model="searchForm.organization" placeholder="请选择" class="search-select">
          <el-option label="全部" value="" />
          <el-option label="一期工程A区" value="一期工程A区" />
          <el-option label="二期工程B区" value="二期工程B区" />
          <el-option label="地下室C区" value="地下室C区" />
        </el-select>
      </div>
      <div class="search-item">
        <span class="label">在线状态</span>
        <el-select v-model="searchForm.status" placeholder="请选择" class="search-select">
          <el-option label="全部" value="" />
          <el-option label="在线" value="在线" />
          <el-option label="离线" value="离线" />
        </el-select>
      </div>
      <div class="search-item">
        <span class="label">告警状态</span>
        <el-select v-model="searchForm.alarmStatus" placeholder="请选择" class="search-select">
          <el-option label="全部" value="" />
          <el-option label="正常" value="正常" />
          <el-option label="异常" value="异常" />
        </el-select>
      </div>
      <div class="search-item">
        <span class="label">运行状态</span>
        <el-select v-model="searchForm.runStatus" placeholder="请选择" class="search-select">
          <el-option label="全部" value="" />
          <el-option label="正常" value="正常" />
          <el-option label="异常" value="异常" />
        </el-select>
      </div>
      <div class="search-buttons">
        <el-button class="reset-btn" icon="refresh" @click="handleReset">重置</el-button>
        <el-button
          type="primary"
          class="search-btn"
          icon="search"
          @click="handleSearch"
        >查询</el-button>

      </div>
    </div>

    <!-- 设备列表展示 -->
    <div class="device-list-container">
      <div class="device-list">
        <div
          v-for="(device) in deviceList"
          :key="device.deviceId"
          class="device-card"
          :class="{ 'active': activeDevice === device.id }"
          @click="handleDeviceClick(device.deviceId)"
        >
          <div class="device-card-header">
            <span class="device-name">{{ device.deviceName }}</span>
            <span :class="['device-status', device.status === '1' ? 'online' : 'offline']">{{ device.status === '1' ? '在线' : '离线' }}</span>
          </div>
          <div class="device-card-content">
            <div class="device-info-item">
              <span class="info-label">设备SN:</span>
              <span class="info-value">{{ device.imei }}</span>
            </div>
            <div class="device-info-item">
              <span class="info-label">在线状态:</span>
              <span class="info-value">{{ device.status === '1' ? '在线' : '离线' }}</span>
            </div>
            <div class="device-info-item">
              <span class="info-label">所属区域:</span>
              <span class="info-value">{{ device.localUnitText }}</span>
            </div>
            <div class="device-info-item">
              <span class="info-label">安装地址:</span>
              <span class="info-value">{{ device.houseAddress }}</span>
            </div>
            <div class="device-info-item">
              <span class="info-label">更新时间:</span>
              <span class="info-value">{{ device.updateTime||device.createTime }}</span>
            </div>
          </div>

        </div>
      </div>
    </div>

    <!-- 分页信息 -->
    <div class="pagination-container">
      <div class="pagination-info">
        <span>共{{ total }}条</span>
      </div>
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 40]"
        layout="prev, pager, next, jumper, sizes"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <HistoryReportDialog
      v-if="showDialog"
      :visible="showDialog"
      :device-id="dialogDeviceId"
      @close="showDialog = false"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { getCockpitDeviceList } from '@/api/cockpit'
import HistoryReportDialog from './HistoryReportDialog.vue'

// 搜索表单
const searchForm = reactive({
  deviceName: '',
  organization: '',
  status: '',
  alarmStatus: '',
  runStatus: ''
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(15)
const total = ref(0)

// 当前选中的设备
const activeDevice = ref(null)
const dialogDeviceId = ref(null)
const showDialog = ref(false)

// 设备数据
const deviceList = ref([])

// 获取设备数据（接口）
const fetchDeviceList = async() => {
  const params = {
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    deviceName: searchForm.deviceName,
    organization: searchForm.organization,
    status: searchForm.status,
    alarmStatus: searchForm.alarmStatus,
    runStatus: searchForm.runStatus
  }
  try {
    const res = await getCockpitDeviceList(params)
    // 假设返回结构为 { rows: [], total: 0 }，如有不同请调整
    deviceList.value = res.rows || []
    total.value = res.total || 0
    // 默认选中第一个设备
    if (deviceList.value.length > 0) {
      activeDevice.value = deviceList.value[0].id
    } else {
      activeDevice.value = null
    }
  } catch (e) {
    deviceList.value = []
    total.value = 0
    activeDevice.value = null
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchDeviceList()
}

// 重置搜索条件
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  currentPage.value = 1
  fetchDeviceList()
}

// 处理设备点击
const handleDeviceClick = (id) => {
  activeDevice.value = id
  dialogDeviceId.value = id
  showDialog.value = true
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchDeviceList()
}

// 处理每页条数变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  fetchDeviceList()
}
fetchDeviceList()

</script>

<style scoped>
.device-info {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  color: rgba(255, 255, 255, 0.8);
}

/* 搜索区域样式 */
.search-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 16px;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 16px;
  background: rgba(35, 66, 127, 0.3);
  border-radius: 4px;
}

.search-item {
  display: flex;
  align-items: center;

}

.label {
  margin-right: 8px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
}

.search-input,
.search-select {
  background: rgba(35, 66, 127, 0.3);
  height: 32px;
  width: 280px;
}

:deep(.el-input__wrapper),
:deep(.el-select .el-input__wrapper) {
  background-color: rgba(35, 66, 127, 0.3);
  box-shadow: none;
  border: 1px solid rgba(35, 104, 240, 0.3);
}

:deep(.el-input__inner) {
  color: rgba(255, 255, 255, 0.8);
  height: 32px;
}

:deep(.el-select .el-input__inner) {
  color: rgba(255, 255, 255, 0.8);
}

.search-buttons {
  width:346px ;
  display: flex;
  justify-content: flex-end;
  gap: 8px;

}

.search-btn {
  background: #165DFF;
  border: none;
}

.reset-btn {
  background: rgba(35, 66, 127, 0.3);
  border: 1px solid rgba(35, 104, 240, 0.3);
  color: rgba(255, 255, 255, 0.8);
}

/* 设备列表样式 */
.device-list-container {
  flex: 1;
  overflow-y: auto;
}

.device-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding-bottom: 16px;
}

.device-card {
  width: 232px;
  height: 152px;
  background: rgba(35, 66, 127, 0.2);
  border: 1px solid rgba(35, 104, 240, 0.3);
  border-radius: 4px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: all 0.3s ease;
}

.device-card:hover {
  background: rgba(35, 66, 127, 0.4);
  border-color: rgba(0, 147, 255, 0.5);
}

.device-card.active {
  background: url(@/assets/screen/data-selected.png) no-repeat;
  background-size: 100% 100%;
  border-color: rgba(0, 147, 255, 0.8);
}

.device-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.device-name {
  font-size: 14px;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.9);
}

.device-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 2px;
}

.device-status.online {
  background: rgba(0, 255, 0, 0.2);
  color: #00FF00;
}

.device-status.offline {
  background: rgba(255, 0, 0, 0.2);
  color: #FF6B6B;
}

.device-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
}

.device-info-item {
  display: flex;
  align-items: center;
}

.info-label {
  color: rgba(255, 255, 255, 0.6);
  margin-right: 4px;
  width: 70px;
}

.info-value {
  color: rgba(255, 255, 255, 0.8);
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.device-card-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 4px;
}

.run-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 2px;
}

.run-status.normal {
  background: rgba(0, 255, 0, 0.2);
  color: #00FF00;
}

.run-status.abnormal {
  background: rgba(255, 0, 0, 0.2);
  color: #FF6B6B;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
}

.pagination-info {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

:deep(.el-pagination) {
  --el-pagination-bg-color: transparent;
  --el-pagination-text-color: rgba(255, 255, 255, 0.6);
  --el-pagination-button-color: rgba(255, 255, 255, 0.6);
  --el-pagination-button-bg-color: rgba(35, 66, 127, 0.3);
  --el-pagination-button-disabled-color: rgba(255, 255, 255, 0.3);
  --el-pagination-button-disabled-bg-color: rgba(35, 66, 127, 0.1);
  --el-pagination-hover-color: #00FFFF;
}

:deep(.el-pagination .el-select .el-input) {
  width: 100px;
}

:deep(.el-pagination .el-select .el-input .el-input__inner) {
  color: rgba(255, 255, 255, 0.8);
}

:deep(.el-pagination .el-select .el-input__wrapper) {
  background-color: rgba(35, 66, 127, 0.3);
  box-shadow: none;
  border: 1px solid rgba(35, 104, 240, 0.3);
}

:deep(.device-info-select-scope) .el-select-dropdown,
:deep(.device-info-select-scope) .el-select-dropdown__wrap,
:deep(.device-info-select-scope) .el-select-dropdown__list,
:deep(.device-info-select-scope) .el-select-dropdown__item {
  background-color: rgba(35, 66, 127, 0.3) !important;
  border: none !important;
}

:deep(.device-info-select-scope) .el-select-dropdown__item {
  color: rgba(255, 255, 255, 0.8) !important;
}

:deep(.device-info-select-scope) .el-select-dropdown__item.hover,
:deep(.device-info-select-scope) .el-select-dropdown__item:hover,
:deep(.device-info-select-scope) .el-select-dropdown__item.selected {
  background-color: rgba(35, 104, 240, 0.3) !important;
  color: #00FFFF !important;
}

/* 设置滚动条样式 */
.device-list-container::-webkit-scrollbar {
  width: 6px;
}

.device-list-container::-webkit-scrollbar-thumb {
  background-color: rgba(35, 104, 240, 0.5);
  border-radius: 3px;
}

.device-list-container::-webkit-scrollbar-track {
  background-color: rgba(35, 66, 127, 0.1);
}

:deep(.el-select__wrapper) {
  background-color: rgba(35, 66, 127, 0.3) !important;
  border: 1px solid rgba(35, 104, 240, 0.3) !important;
  box-shadow: 0 0 0 0.5px rgba(35, 104, 240, 0.3) inset;
}

:deep(.el-select__input-wrapper) {

}
</style>
