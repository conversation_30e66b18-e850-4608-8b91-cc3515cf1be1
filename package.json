{"name": "lonely", "version": "3.8.5", "description": "默宁智能监测云平台", "author": "珑莉", "license": "MIT", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/Lonely-Vue.git"}, "dependencies": {"@element-plus/icons-vue": "2.0.10", "@vueup/vue-quill": "1.1.0", "@vueuse/core": "9.5.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "autofit.js": "^3.1.0", "axios": "0.27.2", "echarts": "^5.4.0", "element-plus": "^2.7.2", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-cookie": "3.0.1", "jsencrypt": "3.3.1", "nprogress": "0.2.0", "pinia": "2.0.22", "vue": "3.2.45", "vue-cropper": "1.0.3", "vue-router": "4.1.4"}, "devDependencies": {"@babel/core": "^7.22.5", "@babel/eslint-parser": "^7.22.5", "@unocss/preset-rem-to-px": "^0.65.3", "@vitejs/plugin-vue": "3.1.0", "@vue/compiler-sfc": "3.2.45", "eslint": "^8.43.0", "eslint-plugin-vue": "^9.15.0", "sass": "1.56.1", "unocss": "^0.51.13", "unplugin-auto-import": "0.11.4", "vite": "3.2.3", "vite-plugin-compression": "0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-setup-extend": "0.4.0"}}