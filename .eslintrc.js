// ESlint 检查配置
module.exports = {
  root: true,
  parserOptions: {
    parser: '@babel/eslint-parser',
    requireConfigFile: false,
    sourceType: 'module'
  },
  env: {
    browser: true,
    node: true,
    es6: true
  },
  extends: ['plugin:vue/vue3-recommended', 'eslint:recommended', './.eslintrc-auto-import.json'],
  // plugins: ['vue'],

  // add your custom rules here
  // it is base on https://eslint.vuejs.org/rules/ 0/off: 禁用，1/warn: 警告，2/error: 错误
  rules: {
    'vue/max-attributes-per-line': ['error', { // 单行最多3个属性，多行最多1个属性
      'singleline': {
        max: 3
      },
      'multiline': {
        max: 1
      }
    }],
    'vue/singleline-html-element-content-newline': 'off', // 单行元素内容不换行
    'vue/multiline-html-element-content-newline': 'off', // 多行元素内容不换行
    'vue/name-property-casing': 'off', // 组件名大小写
    'vue/multi-word-component-names': 'off', // 组件名是多个单词
    'vue/require-default-prop': 'off', // props必须有默认值
    'vue/no-mutating-props': 'off', // 禁止修改props
    'vue/no-v-html': 'off', // 禁止使用v-html
    'accessor-pairs': 2, // getter和setter必须成对出现
    'arrow-spacing': [2, { // 箭头函数的箭头前后必须有空格
      'before': true,
      'after': true
    }],
    'block-spacing': [2, 'always'], // 单行代码块中紧贴括号部分不允许包含空格
    'brace-style': [2, '1tbs', { // 大括号风格，允许单行模式
      'allowSingleLine': true
    }],
    'camelcase': [0, { // 强制驼峰法命名
      'properties': 'always'
    }],
    'comma-dangle': [2, 'never'], // 对象字面量项尾不能有逗号
    'comma-spacing': [2, { // 逗号前后的空格
      'before': false,
      'after': true
    }],
    'comma-style': [2, 'last'], // 逗号风格，换行时在行首还是行尾
    'constructor-super': 2, // 非派生类不能调用super，派生类必须调用super
    'curly': [2, 'multi-line'], // 必须使用 if(){} 中的{}
    'dot-location': [2, 'property'], // 对象访问符的位置，换行的时候紧跟属性
    'eol-last': 2, // 文件以单一的换行符结束
    'eqeqeq': ['error', 'always', { 'null': 'ignore' }], // 必须使用全等
    'generator-star-spacing': [2, { // 生成器函数*的前后空格
      'before': true,
      'after': true
    }],
    'handle-callback-err': [2, '^(err|error)$'], // nodejs 处理错误
    'indent': [2, 2, { // 缩进风格
      'SwitchCase': 1 // switch语句中的case标签缩进
    }],
    'jsx-quotes': [2, 'prefer-single'], // 强制在JSX属性（jsx-quotes）中一致使用双引号
    'key-spacing': [2, { // 对象字面量中冒号的前后空格
      'beforeColon': false,
      'afterColon': true
    }],
    'keyword-spacing': [2, { // 关键字前后的空格
      'before': true,
      'after': true
    }],
    'new-cap': [2, { // 构造函数名字首字母大写
      'newIsCap': true,
      'capIsNew': false
    }],
    'new-parens': 2, // new时必须加小括号
    'no-array-constructor': 2, // 禁止使用数组构造器
    'no-caller': 2, // 禁止使用arguments.caller或arguments.callee
    'no-console': 'off', // 禁止使用console
    'no-class-assign': 2, // 禁止给类赋值
    'no-cond-assign': 2, // 禁止在条件表达式中使用赋值语句
    'no-const-assign': 2, // 禁止修改const声明的变量
    'no-control-regex': 0, // 禁止在正则表达式中使用控制字符
    'no-delete-var': 2, // 不能对var声明的变量使用delete操作符
    'no-dupe-args': 2, // 函数参数不能重复
    'no-dupe-class-members': 2, // 禁止类成员中出现重复的名称
    'no-dupe-keys': 2, // 在创建对象字面量时不允许键重复
    'no-duplicate-case': 2, // switch中的case标签不能重复
    'no-empty-character-class': 2, // 正则表达式中的[]内容不能为空
    'no-empty-pattern': 2, // 禁止使用空解构模式
    'no-eval': 2, // 禁止使用eval
    'no-ex-assign': 2, // 禁止给catch语句中的异常参数赋值
    'no-extend-native': 2, // 禁止扩展native对象
    'no-extra-bind': 2, // 禁止不必要的函数绑定
    'no-extra-boolean-cast': 2, // 禁止不必要的bool转换
    'no-extra-parens': [2, 'functions'], // 禁止非必要的括号
    'no-fallthrough': 2, // 禁止switch穿透
    'no-floating-decimal': 2, // 禁止省略浮点数中的0 .5 3.
    'no-func-assign': 2, // 禁止重复的函数声明
    'no-implied-eval': 2, // 禁止使用隐式eval
    'no-inner-declarations': [2, 'functions'], // 禁止在块语句中使用声明（变量或函数）
    'no-invalid-regexp': 2, // 禁止无效的正则表达式
    'no-irregular-whitespace': 2, // 不能有不规则的空格
    'no-iterator': 2, // 禁止使用__iterator__ 属性
    'no-label-var': 2, // label名不能与var声明的变量名相同
    'no-labels': [2, { // 禁止标签声明
      'allowLoop': false,
      'allowSwitch': false
    }],
    'no-lone-blocks': 2, // 禁止不必要的嵌套块
    'no-mixed-spaces-and-tabs': 2, // 禁止混用tab和空格
    'no-multi-spaces': 2, // 不能用多余的空格
    'no-multi-str': 2, // 字符串不能用\换行
    'no-multiple-empty-lines': [2, { // 空行最多不能超过1行
      'max': 1
    }],
    'no-native-reassign': 2, // 不能重写native对象
    'no-negated-in-lhs': 2, // in 操作符的左边不能有!
    'no-new-object': 2, // 禁止使用new Object()
    'no-new-require': 2, // 禁止使用new require
    'no-new-symbol': 2, // 禁止使用new symbol
    'no-new-wrappers': 2, // 禁止使用new创建包装实例，new String new Boolean new Number
    'no-obj-calls': 2, // 不能调用内置的全局对象，比如Math() JSON()
    'no-octal': 2, // 禁止使用八进制数字
    'no-octal-escape': 2, // 禁止使用八进制转义序列
    'no-path-concat': 2, // node中不能使用__dirname或__filename做路径拼接
    'no-proto': 2, // 禁止使用__proto__属性
    'no-redeclare': 2, // 禁止重复声明变量
    'no-regex-spaces': 2, // 禁止在正则表达式字面量中使用多个空格 /foo bar/
    'no-return-assign': [2, 'except-parens'], // return 语句中不能有赋值表达式
    'no-self-assign': 2, // 不能自己给自己赋值
    'no-self-compare': 2, // 不能自身比较
    'no-sequences': 2, // 禁止使用逗号运算符
    'no-shadow-restricted-names': 2, // 不能使用保留字作为变量名
    'no-spaced-func': 2, // 函数调用时 函数名与()之间不能有空格
    'no-sparse-arrays': 2, // 禁止稀疏数组， [1,,2]
    'no-this-before-super': 2, // 在调用super()之前不能使用this或super
    'no-throw-literal': 2, // 禁止抛出字面量错误 throw "error";
    'no-trailing-spaces': 2, // 一行结束后面不要有空格
    'no-undef': 2, // 不能有未定义的变量
    'no-undef-init': 2, // 变量初始化时不能直接给它赋值为undefined
    'no-unexpected-multiline': 2, // 避免多行表达式
    'no-unmodified-loop-condition': 2, // 检查引用是否在循环中被修改
    'no-unneeded-ternary': [2, { // 禁止不必要的嵌套 var isYes = answer === 1 ? true : false;
      'defaultAssignment': false
    }],
    'no-unreachable': 2, // 不能有无法执行的代码
    'no-unsafe-finally': 2, // 禁止在finally语句块中出现控制流语句
    'no-unused-vars': [2, { // 不能有声明后未被使用的变量或参数
      'vars': 'all',
      'args': 'none'
    }],
    'no-useless-call': 2, // 禁止不必要的call和apply
    'no-useless-computed-key': 2, // 禁止在对象中使用不必要的计算属性
    'no-useless-constructor': 2, // 可以在不改变类的工作方式的情况下安全地移除的类构造函数
    'no-useless-escape': 0, // 禁止不必要的转义字符
    'no-whitespace-before-property': 2, // 禁止属性前有空白
    'no-with': 2, // 禁用with
    'one-var': [2, { // 强制函数中的变量要么一起声明要么分开声明
      'initialized': 'never'
    }],
    'operator-linebreak': [2, 'after', { // 换行时运算符在行尾还是行首
      'overrides': {
        '?': 'before',
        ':': 'before'
      }
    }],
    'padded-blocks': [2, 'never'], // 块语句内行首行尾是否要空行
    'quotes': [2, 'single', { // 引号类型 `` "" ''
      'avoidEscape': true,
      'allowTemplateLiterals': true
    }],
    'semi': [2, 'never'], // 语句强制分号结尾
    'semi-spacing': [2, { // 分号前后空格
      'before': false,
      'after': true
    }],
    'space-before-blocks': [2, 'always'], // 不以新行开始的块{前面要不要有空格
    'space-before-function-paren': [2, 'never'], // 函数定义时括号前面要不要有空格
    'space-in-parens': [2, 'never'], // 小括号里面要不要有空格
    'space-infix-ops': 2, // 中缀操作符周围要不要有空格
    'space-unary-ops': [2, { // 一元运算符的前/后要不要加空格
      'words': true,
      'nonwords': false
    }],
    'spaced-comment': [2, 'always', { // 注释风格要不要有空格什么的
      'markers': ['global', 'globals', 'eslint', 'eslint-disable', '*package', '!', ',']// 允许的注释标记
    }],
    'template-curly-spacing': [2, 'never'], // 禁止模板字符串中的嵌入表达式周围空格的使用
    'use-isnan': 2, // 禁止比较时使用NaN，只能用isNaN()
    'valid-typeof': 2, // 必须使用合法的typeof的值
    'wrap-iife': [2, 'any'], // 立即执行函数表达式的小括号风格
    'yield-star-spacing': [2, 'both'], // yield* 表达式中 * 周围空格
    'yoda': [2, 'never'], // 禁止尤达条件
    'prefer-const': 2, // 首选const
    'no-debugger': process.env.NODE_ENV === 'production' ? 2 : 0, // 禁止使用debugger
    'object-curly-spacing': [2, 'always', { // 大括号内是否允许不必要的空格
      objectsInObjects: false
    }],
    'array-bracket-spacing': [2, 'never']// 是否允许非空数组里面有多余的空格
  }
}
